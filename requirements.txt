# AI量化交易工具依赖包列表

# Web框架
fastapi==0.104.1
uvicorn[standard]==0.24.0
pydantic==2.5.0
pydantic-settings==2.1.0

# 数据库相关
sqlalchemy==2.0.23
alembic==1.13.1
psycopg2-binary==2.9.9
redis==5.0.1
clickhouse-driver==0.2.6

# 数据处理
pandas==2.1.4
numpy==1.25.2
polars==0.20.2
pyarrow==14.0.2

# 金融数据和计算
yfinance==0.2.28
tushare==1.2.89
quantlib==1.32
empyrical==0.5.5
pyfolio==0.9.2

# 机器学习
scikit-learn==1.3.2
xgboost==2.0.2
lightgbm==4.1.0
statsmodels==0.14.1

# 技术指标
talib==0.4.28
ta==0.10.2

# 消息队列
kafka-python==2.0.2
celery==5.3.4

# 认证和安全
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4
python-multipart==0.0.6

# HTTP客户端
httpx==0.25.2
requests==2.31.0

# 配置管理
python-dotenv==1.0.0
pyyaml==6.0.1

# 日志和监控
structlog==23.2.0
prometheus-client==0.19.0
opentelemetry-api==1.21.0
opentelemetry-sdk==1.21.0
opentelemetry-instrumentation-fastapi==0.42b0
opentelemetry-instrumentation-sqlalchemy==0.42b0
opentelemetry-exporter-jaeger==1.21.0

# 测试
pytest==7.4.3
pytest-asyncio==0.21.1
pytest-cov==4.1.0
httpx==0.25.2

# 代码质量
black==23.11.0
flake8==6.1.0
isort==5.12.0
mypy==1.7.1

# 工具库
click==8.1.7
rich==13.7.0
typer==0.9.0
schedule==1.2.1

# 数据可视化
plotly==5.17.0
matplotlib==3.8.2
seaborn==0.13.0

# 邮件发送
emails==0.6

# 文件处理
openpyxl==3.1.2
xlsxwriter==3.1.9

# 加密
cryptography==41.0.8

# 时间处理
python-dateutil==2.8.2
pytz==2023.3

# 异步支持
aiofiles==23.2.1
aioredis==2.0.1

# WebSocket
websockets==12.0

# 数据验证
cerberus==1.3.5

# 缓存
cachetools==5.3.2

# 工作流
prefect==2.14.21

# 数学计算
scipy==1.11.4

# 并发处理
concurrent-futures==3.1.1

# 系统监控
psutil==5.9.6

# 环境变量
environs==10.3.0

# JSON处理
orjson==3.9.10

# 时间序列
arch==6.2.0

# 网络请求
aiohttp==3.9.1

# 数据库迁移
yoyo-migrations==8.2.0

# 配置验证
marshmallow==3.20.1

# 任务调度
apscheduler==3.10.4

# 文档生成
mkdocs==1.5.3
mkdocs-material==9.4.8

# 开发工具
pre-commit==3.6.0
bandit==1.7.5
safety==2.3.5
