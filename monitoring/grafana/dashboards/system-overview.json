{"dashboard": {"id": null, "title": "AI量化交易工具系统概览", "tags": ["quantitative", "trading", "system"], "style": "dark", "timezone": "browser", "panels": [{"id": 1, "title": "系统状态概览", "type": "stat", "targets": [{"expr": "up{job=\"quantitative-tools-app\"}", "legendFormat": "应用状态"}], "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "thresholds": {"steps": [{"color": "red", "value": 0}, {"color": "green", "value": 1}]}}}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 0}}, {"id": 2, "title": "HTTP请求速率", "type": "graph", "targets": [{"expr": "rate(http_requests_total{job=\"quantitative-tools-app\"}[5m])", "legendFormat": "{{method}} {{handler}}"}], "yAxes": [{"label": "请求/秒", "min": 0}], "gridPos": {"h": 8, "w": 12, "x": 12, "y": 0}}, {"id": 3, "title": "响应时间", "type": "graph", "targets": [{"expr": "histogram_quantile(0.95, rate(http_request_duration_seconds_bucket{job=\"quantitative-tools-app\"}[5m]))", "legendFormat": "95th percentile"}, {"expr": "histogram_quantile(0.50, rate(http_request_duration_seconds_bucket{job=\"quantitative-tools-app\"}[5m]))", "legendFormat": "50th percentile"}], "yAxes": [{"label": "秒", "min": 0}], "gridPos": {"h": 8, "w": 12, "x": 0, "y": 8}}, {"id": 4, "title": "错误率", "type": "graph", "targets": [{"expr": "rate(http_requests_total{job=\"quantitative-tools-app\",status=~\"5..\"}[5m]) / rate(http_requests_total{job=\"quantitative-tools-app\"}[5m])", "legendFormat": "5xx错误率"}], "yAxes": [{"label": "错误率", "min": 0, "max": 1}], "gridPos": {"h": 8, "w": 12, "x": 12, "y": 8}}], "time": {"from": "now-1h", "to": "now"}, "refresh": "30s"}}