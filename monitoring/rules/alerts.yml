# AI量化交易工具系统告警规则

groups:
  - name: quantitative_tools_alerts
    rules:
      # 应用服务告警
      - alert: ApplicationDown
        expr: up{job="quantitative-tools-app"} == 0
        for: 1m
        labels:
          severity: critical
          service: quantitative-tools
        annotations:
          summary: "AI量化交易工具应用服务宕机"
          description: "应用服务 {{ $labels.instance }} 已宕机超过1分钟"

      - alert: HighErrorRate
        expr: rate(http_requests_total{job="quantitative-tools-app",status=~"5.."}[5m]) / rate(http_requests_total{job="quantitative-tools-app"}[5m]) > 0.1
        for: 5m
        labels:
          severity: warning
          service: quantitative-tools
        annotations:
          summary: "应用错误率过高"
          description: "应用 {{ $labels.instance }} 的5xx错误率超过10%，当前值: {{ $value | humanizePercentage }}"

      - alert: HighResponseTime
        expr: histogram_quantile(0.95, rate(http_request_duration_seconds_bucket{job="quantitative-tools-app"}[5m])) > 2
        for: 5m
        labels:
          severity: warning
          service: quantitative-tools
        annotations:
          summary: "应用响应时间过长"
          description: "应用 {{ $labels.instance }} 的95%响应时间超过2秒，当前值: {{ $value }}s"

      # 数据库告警
      - alert: PostgreSQLDown
        expr: up{job="postgres"} == 0
        for: 1m
        labels:
          severity: critical
          service: database
        annotations:
          summary: "PostgreSQL数据库宕机"
          description: "PostgreSQL数据库 {{ $labels.instance }} 已宕机超过1分钟"

      - alert: PostgreSQLTooManyConnections
        expr: pg_stat_database_numbackends{datname="quantitative_tools"} > 80
        for: 5m
        labels:
          severity: warning
          service: database
        annotations:
          summary: "PostgreSQL连接数过多"
          description: "PostgreSQL数据库连接数超过80，当前值: {{ $value }}"

      # Redis告警
      - alert: RedisDown
        expr: up{job="redis"} == 0
        for: 1m
        labels:
          severity: critical
          service: cache
        annotations:
          summary: "Redis缓存服务宕机"
          description: "Redis缓存服务 {{ $labels.instance }} 已宕机超过1分钟"

      # 系统资源告警
      - alert: HighCPUUsage
        expr: 100 - (avg by(instance) (irate(node_cpu_seconds_total{mode="idle"}[5m])) * 100) > 80
        for: 5m
        labels:
          severity: warning
          service: system
        annotations:
          summary: "系统CPU使用率过高"
          description: "服务器 {{ $labels.instance }} CPU使用率超过80%，当前值: {{ $value }}%"

      - alert: HighMemoryUsage
        expr: (node_memory_MemTotal_bytes - node_memory_MemAvailable_bytes) / node_memory_MemTotal_bytes > 0.9
        for: 5m
        labels:
          severity: warning
          service: system
        annotations:
          summary: "系统内存使用率过高"
          description: "服务器 {{ $labels.instance }} 内存使用率超过90%，当前值: {{ $value | humanizePercentage }}"

      - alert: DiskSpaceLow
        expr: (node_filesystem_avail_bytes{mountpoint="/"} / node_filesystem_size_bytes{mountpoint="/"}) < 0.1
        for: 5m
        labels:
          severity: critical
          service: system
        annotations:
          summary: "磁盘空间不足"
          description: "服务器 {{ $labels.instance }} 根分区可用空间少于10%，当前值: {{ $value | humanizePercentage }}"

      # 业务指标告警
      - alert: TradingSystemDown
        expr: trading_system_status == 0
        for: 1m
        labels:
          severity: critical
          service: trading
        annotations:
          summary: "交易系统异常"
          description: "交易系统状态异常，请立即检查"

      - alert: HighRiskAlert
        expr: portfolio_risk_level > 0.8
        for: 2m
        labels:
          severity: warning
          service: risk
        annotations:
          summary: "投资组合风险过高"
          description: "投资组合风险水平超过80%，当前值: {{ $value | humanizePercentage }}"