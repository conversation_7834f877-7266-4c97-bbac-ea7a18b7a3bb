# AI量化交易工具系统 Prometheus监控配置

global:
  scrape_interval: 15s
  evaluation_interval: 15s

# 规则文件配置
rule_files:
  - "rules/*.yml"

# 告警管理器配置
alerting:
  alertmanagers:
    - static_configs:
        - targets:
          - alertmanager:9093

# 抓取配置
scrape_configs:
  # Prometheus自身监控
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']

  # 主应用监控
  - job_name: 'quantitative-tools-app'
    static_configs:
      - targets: ['app:8000']
    metrics_path: '/metrics'
    scrape_interval: 10s
    scrape_timeout: 5s

  # PostgreSQL监控
  - job_name: 'postgres'
    static_configs:
      - targets: ['postgres-exporter:9187']

  # ClickHouse监控
  - job_name: 'clickhouse'
    static_configs:
      - targets: ['clickhouse:8123']
    metrics_path: '/metrics'

  # Redis监控
  - job_name: 'redis'
    static_configs:
      - targets: ['redis-exporter:9121']

  # Kafka监控
  - job_name: 'kafka'
    static_configs:
      - targets: ['kafka-exporter:9308']

  # Node Exporter监控（系统指标）
  - job_name: 'node'
    static_configs:
      - targets: ['node-exporter:9100']

  # cAdvisor监控（容器指标）
  - job_name: 'cadvisor'
    static_configs:
      - targets: ['cadvisor:8080']

  # 自定义业务指标
  - job_name: 'business-metrics'
    static_configs:
      - targets: ['app:8000']
    metrics_path: '/api/v1/metrics/business'
    scrape_interval: 30s