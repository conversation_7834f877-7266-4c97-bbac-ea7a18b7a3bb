"""
回测引擎

提供完整的策略回测功能，包括：
- 历史数据回测
- 策略性能评估
- 交易成本计算
- 滑点模拟
- 回测结果分析
"""

import logging
import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Any, Callable
from datetime import datetime, date, timedelta
from dataclasses import dataclass, field

logger = logging.getLogger(__name__)


@dataclass
class BacktestConfig:
    """回测配置"""
    start_date: date
    end_date: date
    initial_capital: float = 1000000.0
    commission_rate: float = 0.0003
    min_commission: float = 5.0
    slippage_rate: float = 0.001
    benchmark: str = "000300.SH"


@dataclass
class BacktestResult:
    """回测结果"""
    config: BacktestConfig
    portfolio_values: pd.Series = field(default_factory=pd.Series)
    trades: List[Dict[str, Any]] = field(default_factory=list)
    performance_metrics: Dict[str, float] = field(default_factory=dict)


class BacktestEngine:
    """回测引擎"""

    def __init__(self):
        self._current_positions: Dict[str, float] = {}
        self._current_cash: float = 0.0
        self._trades_history: List[Dict[str, Any]] = []

    def run_backtest(self, strategy, config: BacktestConfig) -> BacktestResult:
        """运行回测"""
        try:
            logger.info(f"开始回测: {config.start_date} 到 {config.end_date}")

            # 初始化
            self._initialize_backtest(config)

            # 生成模拟数据
            market_data = self._generate_mock_data(config)

            # 运行策略
            result = self._execute_strategy(strategy, config, market_data)

            logger.info("回测完成")
            return result

        except Exception as e:
            logger.error(f"回测执行失败: {e}")
            raise

    def _initialize_backtest(self, config: BacktestConfig):
        """初始化回测环境"""
        self._current_positions.clear()
        self._current_cash = config.initial_capital
        self._trades_history.clear()

        logger.info(f"回测初始化完成，初始资金: {config.initial_capital:,.0f}")

    def _generate_mock_data(self, config: BacktestConfig) -> Dict[str, pd.DataFrame]:
        """生成模拟数据"""
        dates = pd.date_range(start=config.start_date, end=config.end_date, freq='D')
        dates = dates[dates.weekday < 5]  # 只保留工作日

        symbols = ['000001.SZ', '000002.SZ']
        market_data = {}

        for symbol in symbols:
            np.random.seed(hash(symbol) % 2**32)

            base_price = 10 + np.random.random() * 20
            returns = np.random.normal(0.0005, 0.02, len(dates))
            prices = [base_price]

            for ret in returns[1:]:
                prices.append(prices[-1] * (1 + ret))

            df = pd.DataFrame({
                'date': dates,
                'close': prices,
                'volume': np.random.randint(1000000, 10000000, len(dates))
            })

            market_data[symbol] = df

        return market_data

    def _execute_strategy(self, strategy, config: BacktestConfig,
                         market_data: Dict[str, pd.DataFrame]) -> BacktestResult:
        """执行策略回测"""
        all_dates = set()
        for df in market_data.values():
            all_dates.update(df['date'])

        trading_dates = sorted(all_dates)
        portfolio_values = []

        for current_date in trading_dates:
            try:
                # 准备当日数据
                daily_data = {}
                for symbol, df in market_data.items():
                    day_data = df[df['date'] == current_date]
                    if not day_data.empty:
                        daily_data[symbol] = day_data.iloc[0]

                if not daily_data:
                    continue

                # 生成交易信号（简化）
                signals = self._generate_simple_signals(daily_data)

                # 执行交易
                for signal in signals:
                    self._execute_trade(signal, daily_data, config)

                # 计算组合价值
                portfolio_value = self._calculate_portfolio_value(daily_data)
                portfolio_values.append(portfolio_value)

            except Exception as e:
                logger.error(f"策略执行失败 {current_date}: {e}")
                continue

        # 构建结果
        portfolio_series = pd.Series(portfolio_values, index=trading_dates[:len(portfolio_values)])
        performance_metrics = self._calculate_performance_metrics(portfolio_series, config)

        result = BacktestResult(
            config=config,
            portfolio_values=portfolio_series,
            trades=self._trades_history,
            performance_metrics=performance_metrics
        )

        return result

    def _generate_simple_signals(self, daily_data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """生成简单交易信号"""
        signals = []

        # 简单的随机信号生成
        for symbol in daily_data.keys():
            if np.random.random() < 0.1:  # 10%概率生成信号
                action = 'buy' if np.random.random() < 0.6 else 'sell'
                signals.append({
                    'symbol': symbol,
                    'action': action,
                    'quantity': 100
                })

        return signals

    def _execute_trade(self, signal: Dict[str, Any], market_data: Dict[str, Any],
                      config: BacktestConfig):
        """执行交易"""
        try:
            symbol = signal.get('symbol')
            action = signal.get('action')
            quantity = signal.get('quantity', 100)

            if symbol not in market_data:
                return

            price = market_data[symbol]['close']

            if action == 'buy':
                total_cost = quantity * price * (1 + config.commission_rate)
                if self._current_cash >= total_cost:
                    self._current_cash -= total_cost
                    self._current_positions[symbol] = self._current_positions.get(symbol, 0) + quantity

                    self._trades_history.append({
                        'date': market_data[symbol]['date'],
                        'symbol': symbol,
                        'side': 'buy',
                        'quantity': quantity,
                        'price': price
                    })

            elif action == 'sell':
                current_position = self._current_positions.get(symbol, 0)
                if current_position >= quantity:
                    self._current_cash += quantity * price * (1 - config.commission_rate)
                    self._current_positions[symbol] = current_position - quantity

                    if self._current_positions[symbol] == 0:
                        del self._current_positions[symbol]

                    self._trades_history.append({
                        'date': market_data[symbol]['date'],
                        'symbol': symbol,
                        'side': 'sell',
                        'quantity': quantity,
                        'price': price
                    })

        except Exception as e:
            logger.error(f"交易执行失败: {e}")

    def _calculate_portfolio_value(self, market_data: Dict[str, Any]) -> float:
        """计算组合价值"""
        total_value = self._current_cash

        for symbol, quantity in self._current_positions.items():
            if symbol in market_data:
                price = market_data[symbol]['close']
                total_value += quantity * price

        return total_value

    def _calculate_performance_metrics(self, portfolio_values: pd.Series,
                                     config: BacktestConfig) -> Dict[str, float]:
        """计算性能指标"""
        if len(portfolio_values) == 0:
            return {}

        returns = portfolio_values.pct_change().dropna()

        total_return = (portfolio_values.iloc[-1] / portfolio_values.iloc[0]) - 1
        annual_return = (1 + total_return) ** (252 / len(portfolio_values)) - 1
        volatility = returns.std() * np.sqrt(252) if len(returns) > 0 else 0

        # 最大回撤
        cumulative_max = portfolio_values.expanding().max()
        drawdown = (portfolio_values - cumulative_max) / cumulative_max
        max_drawdown = abs(drawdown.min())

        # 夏普比率
        risk_free_rate = 0.03
        sharpe_ratio = (annual_return - risk_free_rate) / volatility if volatility > 0 else 0

        return {
            'total_return': total_return,
            'annual_return': annual_return,
            'volatility': volatility,
            'max_drawdown': max_drawdown,
            'sharpe_ratio': sharpe_ratio,
            'total_trades': len(self._trades_history)
        }


# 全局回测引擎实例
backtest_engine = BacktestEngine()