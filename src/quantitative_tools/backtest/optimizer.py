"""
参数优化器

提供策略参数优化功能，包括：
- 网格搜索优化
- 遗传算法优化
- 贝叶斯优化
- 参数敏感性分析
- 优化结果评估
"""

import logging
import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Any, Callable, Tuple
from datetime import datetime
from concurrent.futures import ThreadPoolExecutor, as_completed
import itertools

from .engine import BacktestEngine, BacktestConfig, BacktestResult

logger = logging.getLogger(__name__)


class ParameterOptimizer:
    """参数优化器"""

    def __init__(self, backtest_engine: BacktestEngine = None):
        self.backtest_engine = backtest_engine or BacktestEngine()
        self.optimization_results: List[Dict[str, Any]] = []

    def grid_search_optimization(self, strategy_class, config: BacktestConfig,
                                param_grid: Dict[str, List[Any]],
                                objective_function: str = 'sharpe_ratio',
                                max_workers: int = 4) -> Dict[str, Any]:
        """网格搜索优化"""
        try:
            logger.info(f"开始网格搜索优化，参数组合数: {self._count_combinations(param_grid)}")

            # 生成所有参数组合
            param_combinations = self._generate_param_combinations(param_grid)

            # 并行执行回测
            results = []
            with ThreadPoolExecutor(max_workers=max_workers) as executor:
                future_to_params = {}

                for params in param_combinations:
                    future = executor.submit(
                        self._run_single_backtest,
                        strategy_class, config, params
                    )
                    future_to_params[future] = params

                for future in as_completed(future_to_params):
                    params = future_to_params[future]
                    try:
                        result = future.result()
                        if result and result.performance_metrics:
                            results.append({
                                'parameters': params,
                                'metrics': result.performance_metrics,
                                'objective_value': result.performance_metrics.get(objective_function, 0)
                            })
                    except Exception as e:
                        logger.error(f"回测失败 {params}: {e}")

            # 找到最优参数
            if results:
                best_result = max(results, key=lambda x: x['objective_value'])

                optimization_result = {
                    'method': 'grid_search',
                    'objective_function': objective_function,
                    'best_parameters': best_result['parameters'],
                    'best_metrics': best_result['metrics'],
                    'best_objective_value': best_result['objective_value'],
                    'total_combinations': len(param_combinations),
                    'successful_runs': len(results),
                    'all_results': results,
                    'optimization_time': datetime.now()
                }

                self.optimization_results.append(optimization_result)

                logger.info(f"网格搜索完成，最优{objective_function}: {best_result['objective_value']:.4f}")
                return optimization_result
            else:
                logger.error("没有成功的回测结果")
                return {'error': '没有成功的回测结果'}

        except Exception as e:
            logger.error(f"网格搜索优化失败: {e}")
            return {'error': str(e)}

    def random_search_optimization(self, strategy_class, config: BacktestConfig,
                                  param_ranges: Dict[str, Tuple[Any, Any]],
                                  n_iterations: int = 100,
                                  objective_function: str = 'sharpe_ratio') -> Dict[str, Any]:
        """随机搜索优化"""
        try:
            logger.info(f"开始随机搜索优化，迭代次数: {n_iterations}")

            results = []

            for i in range(n_iterations):
                # 随机生成参数
                params = self._generate_random_params(param_ranges)

                try:
                    result = self._run_single_backtest(strategy_class, config, params)
                    if result and result.performance_metrics:
                        results.append({
                            'parameters': params,
                            'metrics': result.performance_metrics,
                            'objective_value': result.performance_metrics.get(objective_function, 0)
                        })

                        if (i + 1) % 20 == 0:
                            logger.info(f"完成 {i + 1}/{n_iterations} 次迭代")

                except Exception as e:
                    logger.error(f"回测失败 {params}: {e}")

            # 找到最优参数
            if results:
                best_result = max(results, key=lambda x: x['objective_value'])

                optimization_result = {
                    'method': 'random_search',
                    'objective_function': objective_function,
                    'best_parameters': best_result['parameters'],
                    'best_metrics': best_result['metrics'],
                    'best_objective_value': best_result['objective_value'],
                    'total_iterations': n_iterations,
                    'successful_runs': len(results),
                    'all_results': results,
                    'optimization_time': datetime.now()
                }

                self.optimization_results.append(optimization_result)

                logger.info(f"随机搜索完成，最优{objective_function}: {best_result['objective_value']:.4f}")
                return optimization_result
            else:
                logger.error("没有成功的回测结果")
                return {'error': '没有成功的回测结果'}

        except Exception as e:
            logger.error(f"随机搜索优化失败: {e}")
            return {'error': str(e)}

    def _generate_param_combinations(self, param_grid: Dict[str, List[Any]]) -> List[Dict[str, Any]]:
        """生成所有参数组合"""
        keys = list(param_grid.keys())
        values = list(param_grid.values())

        combinations = []
        for combination in itertools.product(*values):
            param_dict = dict(zip(keys, combination))
            combinations.append(param_dict)

        return combinations

    def _count_combinations(self, param_grid: Dict[str, List[Any]]) -> int:
        """计算参数组合数量"""
        count = 1
        for values in param_grid.values():
            count *= len(values)
        return count

    def _generate_random_params(self, param_ranges: Dict[str, Tuple[Any, Any]]) -> Dict[str, Any]:
        """生成随机参数"""
        params = {}

        for param_name, (min_val, max_val) in param_ranges.items():
            if isinstance(min_val, int) and isinstance(max_val, int):
                params[param_name] = np.random.randint(min_val, max_val + 1)
            elif isinstance(min_val, float) or isinstance(max_val, float):
                params[param_name] = np.random.uniform(min_val, max_val)
            else:
                # 对于其他类型，随机选择
                params[param_name] = np.random.choice([min_val, max_val])

        return params

    def _run_single_backtest(self, strategy_class, config: BacktestConfig,
                           params: Dict[str, Any]) -> Optional[BacktestResult]:
        """运行单次回测"""
        try:
            # 创建策略实例并设置参数
            from ..strategy.base import StrategyContext

            context = StrategyContext(
                strategy_id=f"opt_{hash(str(params)) % 10000}",
                name="优化策略",
                parameters=params
            )

            strategy = strategy_class(context)

            # 运行回测
            result = self.backtest_engine.run_backtest(strategy, config)
            return result

        except Exception as e:
            logger.error(f"单次回测失败: {e}")
            return None

    def analyze_parameter_sensitivity(self, optimization_result: Dict[str, Any]) -> Dict[str, Any]:
        """分析参数敏感性"""
        try:
            if 'all_results' not in optimization_result:
                return {'error': '缺少详细结果数据'}

            results = optimization_result['all_results']
            objective_function = optimization_result['objective_function']

            # 提取所有参数和目标值
            all_params = []
            all_objectives = []

            for result in results:
                all_params.append(result['parameters'])
                all_objectives.append(result['objective_value'])

            # 转换为DataFrame便于分析
            params_df = pd.DataFrame(all_params)
            params_df['objective'] = all_objectives

            # 计算每个参数的相关性
            sensitivity_analysis = {}

            for param_name in params_df.columns:
                if param_name == 'objective':
                    continue

                try:
                    correlation = params_df[param_name].corr(params_df['objective'])

                    # 计算参数的统计信息
                    param_stats = {
                        'correlation': correlation,
                        'mean': params_df[param_name].mean(),
                        'std': params_df[param_name].std(),
                        'min': params_df[param_name].min(),
                        'max': params_df[param_name].max()
                    }

                    # 找出该参数的最优值范围
                    top_10_percent = params_df.nlargest(int(len(params_df) * 0.1), 'objective')
                    if not top_10_percent.empty:
                        param_stats['optimal_range'] = {
                            'min': top_10_percent[param_name].min(),
                            'max': top_10_percent[param_name].max(),
                            'mean': top_10_percent[param_name].mean()
                        }

                    sensitivity_analysis[param_name] = param_stats

                except Exception as e:
                    logger.error(f"参数 {param_name} 敏感性分析失败: {e}")

            # 按相关性排序
            sorted_params = sorted(
                sensitivity_analysis.items(),
                key=lambda x: abs(x[1]['correlation']),
                reverse=True
            )

            return {
                'parameter_sensitivity': dict(sorted_params),
                'most_sensitive_param': sorted_params[0][0] if sorted_params else None,
                'least_sensitive_param': sorted_params[-1][0] if sorted_params else None,
                'analysis_summary': self._generate_sensitivity_summary(sensitivity_analysis)
            }

        except Exception as e:
            logger.error(f"参数敏感性分析失败: {e}")
            return {'error': str(e)}

    def _generate_sensitivity_summary(self, sensitivity_analysis: Dict[str, Any]) -> List[str]:
        """生成敏感性分析总结"""
        summary = []

        # 找出高敏感性参数
        high_sensitivity_params = [
            param for param, stats in sensitivity_analysis.items()
            if abs(stats['correlation']) > 0.3
        ]

        if high_sensitivity_params:
            summary.append(f"高敏感性参数: {', '.join(high_sensitivity_params)}")

        # 找出低敏感性参数
        low_sensitivity_params = [
            param for param, stats in sensitivity_analysis.items()
            if abs(stats['correlation']) < 0.1
        ]

        if low_sensitivity_params:
            summary.append(f"低敏感性参数: {', '.join(low_sensitivity_params)}")

        # 参数优化建议
        for param, stats in sensitivity_analysis.items():
            correlation = stats['correlation']
            if abs(correlation) > 0.5:
                if correlation > 0:
                    summary.append(f"{param} 与收益正相关，建议适当增大")
                else:
                    summary.append(f"{param} 与收益负相关，建议适当减小")

        return summary

    def get_optimization_history(self) -> List[Dict[str, Any]]:
        """获取优化历史"""
        return self.optimization_results

    def export_optimization_results(self, filepath: str, result_index: int = -1):
        """导出优化结果"""
        try:
            if not self.optimization_results:
                logger.error("没有优化结果可导出")
                return

            result = self.optimization_results[result_index]

            # 准备导出数据
            export_data = {
                'optimization_summary': {
                    'method': result['method'],
                    'objective_function': result['objective_function'],
                    'best_parameters': result['best_parameters'],
                    'best_objective_value': result['best_objective_value'],
                    'optimization_time': result['optimization_time'].isoformat()
                },
                'detailed_results': []
            }

            # 添加详细结果
            for res in result.get('all_results', []):
                export_data['detailed_results'].append({
                    'parameters': res['parameters'],
                    'objective_value': res['objective_value'],
                    'total_return': res['metrics'].get('total_return', 0),
                    'sharpe_ratio': res['metrics'].get('sharpe_ratio', 0),
                    'max_drawdown': res['metrics'].get('max_drawdown', 0)
                })

            # 导出到文件
            import json
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(export_data, f, indent=2, ensure_ascii=False, default=str)

            logger.info(f"优化结果已导出到: {filepath}")

        except Exception as e:
            logger.error(f"导出优化结果失败: {e}")


# 全局参数优化器实例
parameter_optimizer = ParameterOptimizer()