"""
性能分析器

提供详细的回测结果分析功能，包括：
- 收益率分析
- 风险指标计算
- 基准比较分析
- 交易统计分析
- 可视化图表生成
"""

import logging
import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime
import json

from .engine import BacktestResult

logger = logging.getLogger(__name__)


class PerformanceAnalyzer:
    """性能分析器"""

    def __init__(self):
        self.risk_free_rate = 0.03  # 无风险利率

    def analyze_backtest_result(self, result: BacktestResult) -> Dict[str, Any]:
        """分析回测结果"""
        try:
            if result.portfolio_values.empty:
                return {'error': '没有回测数据'}

            analysis = {
                'basic_metrics': self._calculate_basic_metrics(result),
                'risk_metrics': self._calculate_risk_metrics(result),
                'trade_analysis': self._analyze_trades(result),
                'period_analysis': self._analyze_periods(result),
                'drawdown_analysis': self._analyze_drawdowns(result)
            }

            return analysis

        except Exception as e:
            logger.error(f"性能分析失败: {e}")
            return {'error': str(e)}

    def _calculate_basic_metrics(self, result: BacktestResult) -> Dict[str, float]:
        """计算基础指标"""
        portfolio_values = result.portfolio_values

        if len(portfolio_values) == 0:
            return {}

        # 收益率计算
        total_return = (portfolio_values.iloc[-1] / portfolio_values.iloc[0]) - 1

        # 年化收益率
        days = (portfolio_values.index[-1] - portfolio_values.index[0]).days
        annual_return = (1 + total_return) ** (365 / days) - 1 if days > 0 else 0

        # 日收益率
        daily_returns = portfolio_values.pct_change().dropna()

        return {
            'initial_capital': portfolio_values.iloc[0],
            'final_capital': portfolio_values.iloc[-1],
            'total_return': total_return,
            'annual_return': annual_return,
            'average_daily_return': daily_returns.mean(),
            'trading_days': len(portfolio_values),
            'calendar_days': days
        }

    def _calculate_risk_metrics(self, result: BacktestResult) -> Dict[str, float]:
        """计算风险指标"""
        portfolio_values = result.portfolio_values

        if len(portfolio_values) == 0:
            return {}

        daily_returns = portfolio_values.pct_change().dropna()

        # 波动率
        volatility = daily_returns.std() * np.sqrt(252)

        # 最大回撤
        cumulative_max = portfolio_values.expanding().max()
        drawdown = (portfolio_values - cumulative_max) / cumulative_max
        max_drawdown = abs(drawdown.min())

        # VaR计算
        var_95 = abs(np.percentile(daily_returns, 5)) if len(daily_returns) > 0 else 0
        var_99 = abs(np.percentile(daily_returns, 1)) if len(daily_returns) > 0 else 0

        # 夏普比率
        excess_return = daily_returns.mean() * 252 - self.risk_free_rate
        sharpe_ratio = excess_return / volatility if volatility > 0 else 0

        # 索提诺比率（下行风险调整收益）
        downside_returns = daily_returns[daily_returns < 0]
        downside_volatility = downside_returns.std() * np.sqrt(252) if len(downside_returns) > 0 else 0
        sortino_ratio = excess_return / downside_volatility if downside_volatility > 0 else 0

        # 卡尔马比率
        calmar_ratio = (daily_returns.mean() * 252) / max_drawdown if max_drawdown > 0 else 0

        return {
            'volatility': volatility,
            'max_drawdown': max_drawdown,
            'var_95': var_95,
            'var_99': var_99,
            'sharpe_ratio': sharpe_ratio,
            'sortino_ratio': sortino_ratio,
            'calmar_ratio': calmar_ratio,
            'skewness': daily_returns.skew() if len(daily_returns) > 0 else 0,
            'kurtosis': daily_returns.kurtosis() if len(daily_returns) > 0 else 0
        }

    def _analyze_trades(self, result: BacktestResult) -> Dict[str, Any]:
        """分析交易统计"""
        trades = result.trades

        if not trades:
            return {'total_trades': 0}

        trades_df = pd.DataFrame(trades)

        # 基础统计
        total_trades = len(trades)
        buy_trades = len(trades_df[trades_df['side'] == 'buy'])
        sell_trades = len(trades_df[trades_df['side'] == 'sell'])

        # 交易频率分析
        if 'date' in trades_df.columns:
            trades_df['date'] = pd.to_datetime(trades_df['date'])
            trading_days = trades_df['date'].dt.date.nunique()
            avg_trades_per_day = total_trades / trading_days if trading_days > 0 else 0
        else:
            avg_trades_per_day = 0

        # 股票交易分析
        symbol_trades = trades_df['symbol'].value_counts().to_dict() if 'symbol' in trades_df.columns else {}

        return {
            'total_trades': total_trades,
            'buy_trades': buy_trades,
            'sell_trades': sell_trades,
            'avg_trades_per_day': avg_trades_per_day,
            'unique_symbols': len(symbol_trades),
            'most_traded_symbols': dict(list(symbol_trades.items())[:5]) if symbol_trades else {}
        }

    def _analyze_periods(self, result: BacktestResult) -> Dict[str, Any]:
        """分析不同时期表现"""
        portfolio_values = result.portfolio_values

        if len(portfolio_values) == 0:
            return {}

        # 月度收益率
        monthly_returns = portfolio_values.resample('M').last().pct_change().dropna()

        # 季度收益率
        quarterly_returns = portfolio_values.resample('Q').last().pct_change().dropna()

        # 年度收益率
        yearly_returns = portfolio_values.resample('Y').last().pct_change().dropna()

        return {
            'monthly_stats': {
                'avg_return': monthly_returns.mean(),
                'std_return': monthly_returns.std(),
                'best_month': monthly_returns.max(),
                'worst_month': monthly_returns.min(),
                'positive_months': (monthly_returns > 0).sum(),
                'total_months': len(monthly_returns)
            },
            'quarterly_stats': {
                'avg_return': quarterly_returns.mean(),
                'best_quarter': quarterly_returns.max(),
                'worst_quarter': quarterly_returns.min()
            },
            'yearly_stats': {
                'avg_return': yearly_returns.mean(),
                'best_year': yearly_returns.max(),
                'worst_year': yearly_returns.min()
            }
        }

    def _analyze_drawdowns(self, result: BacktestResult) -> Dict[str, Any]:
        """分析回撤情况"""
        portfolio_values = result.portfolio_values

        if len(portfolio_values) == 0:
            return {}

        # 计算回撤序列
        cumulative_max = portfolio_values.expanding().max()
        drawdown = (portfolio_values - cumulative_max) / cumulative_max

        # 找出所有回撤期
        drawdown_periods = []
        in_drawdown = False
        start_date = None

        for date, dd in drawdown.items():
            if dd < 0 and not in_drawdown:
                # 开始回撤
                in_drawdown = True
                start_date = date
            elif dd >= 0 and in_drawdown:
                # 结束回撤
                in_drawdown = False
                if start_date:
                    period_drawdown = drawdown[start_date:date]
                    max_dd = abs(period_drawdown.min())
                    duration = (date - start_date).days

                    drawdown_periods.append({
                        'start_date': start_date,
                        'end_date': date,
                        'max_drawdown': max_dd,
                        'duration_days': duration
                    })

        # 统计回撤信息
        if drawdown_periods:
            max_drawdown_period = max(drawdown_periods, key=lambda x: x['max_drawdown'])
            longest_drawdown = max(drawdown_periods, key=lambda x: x['duration_days'])
            avg_drawdown = np.mean([dd['max_drawdown'] for dd in drawdown_periods])
            avg_duration = np.mean([dd['duration_days'] for dd in drawdown_periods])
        else:
            max_drawdown_period = None
            longest_drawdown = None
            avg_drawdown = 0
            avg_duration = 0

        return {
            'total_drawdown_periods': len(drawdown_periods),
            'max_drawdown_period': max_drawdown_period,
            'longest_drawdown_period': longest_drawdown,
            'avg_drawdown': avg_drawdown,
            'avg_duration_days': avg_duration,
            'current_drawdown': abs(drawdown.iloc[-1]) if len(drawdown) > 0 else 0
        }

    def compare_with_benchmark(self, result: BacktestResult,
                             benchmark_data: pd.Series) -> Dict[str, Any]:
        """与基准比较"""
        try:
            portfolio_values = result.portfolio_values

            if portfolio_values.empty or benchmark_data.empty:
                return {'error': '数据不足'}

            # 对齐数据
            aligned_data = pd.DataFrame({
                'portfolio': portfolio_values,
                'benchmark': benchmark_data
            }).dropna()

            if aligned_data.empty:
                return {'error': '无法对齐数据'}

            # 计算收益率
            portfolio_returns = aligned_data['portfolio'].pct_change().dropna()
            benchmark_returns = aligned_data['benchmark'].pct_change().dropna()

            # 超额收益
            excess_returns = portfolio_returns - benchmark_returns

            # 跟踪误差
            tracking_error = excess_returns.std() * np.sqrt(252)

            # 信息比率
            information_ratio = excess_returns.mean() * 252 / tracking_error if tracking_error > 0 else 0

            # Beta系数
            covariance = np.cov(portfolio_returns, benchmark_returns)[0][1]
            benchmark_variance = benchmark_returns.var()
            beta = covariance / benchmark_variance if benchmark_variance > 0 else 0

            # Alpha
            portfolio_annual_return = portfolio_returns.mean() * 252
            benchmark_annual_return = benchmark_returns.mean() * 252
            alpha = portfolio_annual_return - (self.risk_free_rate + beta * (benchmark_annual_return - self.risk_free_rate))

            return {
                'alpha': alpha,
                'beta': beta,
                'tracking_error': tracking_error,
                'information_ratio': information_ratio,
                'correlation': portfolio_returns.corr(benchmark_returns),
                'excess_return': excess_returns.mean() * 252,
                'portfolio_annual_return': portfolio_annual_return,
                'benchmark_annual_return': benchmark_annual_return
            }

        except Exception as e:
            logger.error(f"基准比较失败: {e}")
            return {'error': str(e)}

    def generate_performance_report(self, result: BacktestResult) -> Dict[str, Any]:
        """生成完整的性能报告"""
        try:
            analysis = self.analyze_backtest_result(result)

            report = {
                'report_generated_at': datetime.now().isoformat(),
                'backtest_period': {
                    'start_date': result.config.start_date.isoformat(),
                    'end_date': result.config.end_date.isoformat(),
                    'initial_capital': result.config.initial_capital
                },
                'performance_summary': {
                    **analysis.get('basic_metrics', {}),
                    **analysis.get('risk_metrics', {})
                },
                'trade_summary': analysis.get('trade_analysis', {}),
                'period_analysis': analysis.get('period_analysis', {}),
                'drawdown_analysis': analysis.get('drawdown_analysis', {}),
                'recommendations': self._generate_recommendations(analysis)
            }

            return report

        except Exception as e:
            logger.error(f"生成性能报告失败: {e}")
            return {'error': str(e)}

    def _generate_recommendations(self, analysis: Dict[str, Any]) -> List[str]:
        """生成改进建议"""
        recommendations = []

        risk_metrics = analysis.get('risk_metrics', {})
        basic_metrics = analysis.get('basic_metrics', {})

        # 基于夏普比率的建议
        sharpe_ratio = risk_metrics.get('sharpe_ratio', 0)
        if sharpe_ratio < 1.0:
            recommendations.append("夏普比率较低，建议优化风险收益比")
        elif sharpe_ratio > 2.0:
            recommendations.append("夏普比率表现优秀，策略风险调整收益良好")

        # 基于最大回撤的建议
        max_drawdown = risk_metrics.get('max_drawdown', 0)
        if max_drawdown > 0.2:
            recommendations.append("最大回撤过大，建议加强风险控制")
        elif max_drawdown < 0.05:
            recommendations.append("回撤控制良好，可考虑适当提高收益目标")

        # 基于波动率的建议
        volatility = risk_metrics.get('volatility', 0)
        if volatility > 0.3:
            recommendations.append("波动率较高，建议分散投资降低风险")

        # 基于交易频率的建议
        trade_analysis = analysis.get('trade_analysis', {})
        avg_trades_per_day = trade_analysis.get('avg_trades_per_day', 0)
        if avg_trades_per_day > 10:
            recommendations.append("交易频率较高，注意交易成本对收益的影响")

        if not recommendations:
            recommendations.append("策略表现正常，继续监控和优化")

        return recommendations


# 全局性能分析器实例
performance_analyzer = PerformanceAnalyzer()