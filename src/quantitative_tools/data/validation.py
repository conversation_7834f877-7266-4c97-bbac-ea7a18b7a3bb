"""
数据质量管理模块

提供全面的数据验证和质量控制功能，包括：
- 数据完整性检查
- 数据一致性验证
- 异常值检测和处理
- 数据质量评分
- 数据清洗规则引擎
"""

import logging
import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Any, Tuple, Callable
from datetime import datetime, date
from dataclasses import dataclass
from enum import Enum
import re

logger = logging.getLogger(__name__)


class ValidationLevel(Enum):
    """验证级别"""
    INFO = "info"
    WARNING = "warning"
    ERROR = "error"
    CRITICAL = "critical"


@dataclass
class ValidationResult:
    """验证结果"""
    field: str
    level: ValidationLevel
    message: str
    count: int = 0
    percentage: float = 0.0
    details: Dict[str, Any] = None

    def __post_init__(self):
        if self.details is None:
            self.details = {}


class DataValidator:
    """数据验证器"""

    def __init__(self):
        self._rules = {}
        self._custom_validators = {}
        self._quality_thresholds = {
            'completeness': 0.95,  # 完整性阈值
            'accuracy': 0.98,      # 准确性阈值
            'consistency': 0.99,   # 一致性阈值
            'validity': 0.97       # 有效性阈值
        }

    def validate_dataframe(self, df: pd.DataFrame, schema: Dict[str, Any] = None) -> List[ValidationResult]:
        """验证DataFrame数据质量"""
        results = []

        if df.empty:
            results.append(ValidationResult(
                field="dataframe",
                level=ValidationLevel.ERROR,
                message="数据框为空",
                count=0
            ))
            return results

        # 基础验证
        results.extend(self._validate_basic_quality(df))

        # 字段级验证
        for column in df.columns:
            results.extend(self._validate_column(df, column))

        # 业务规则验证
        if schema:
            results.extend(self._validate_business_rules(df, schema))

        logger.info(f"数据验证完成，发现 {len(results)} 个问题")
        return results

    def _validate_basic_quality(self, df: pd.DataFrame) -> List[ValidationResult]:
        """基础数据质量验证"""
        results = []
        total_cells = df.size

        # 检查空值
        null_count = df.isnull().sum().sum()
        if null_count > 0:
            null_percentage = (null_count / total_cells) * 100
            level = ValidationLevel.ERROR if null_percentage > 5 else ValidationLevel.WARNING

            results.append(ValidationResult(
                field="dataframe",
                level=level,
                message=f"发现空值",
                count=null_count,
                percentage=null_percentage,
                details={'null_columns': df.isnull().sum().to_dict()}
            ))

        # 检查重复行
        duplicate_count = df.duplicated().sum()
        if duplicate_count > 0:
            duplicate_percentage = (duplicate_count / len(df)) * 100
            level = ValidationLevel.WARNING if duplicate_percentage < 1 else ValidationLevel.ERROR

            results.append(ValidationResult(
                field="dataframe",
                level=level,
                message=f"发现重复行",
                count=duplicate_count,
                percentage=duplicate_percentage
            ))

        return results

    def _validate_column(self, df: pd.DataFrame, column: str) -> List[ValidationResult]:
        """验证单个列的数据质量"""
        results = []

        if column not in df.columns:
            return results

        series = df[column]

        # 通用验证
        results.extend(self._validate_column_generic(series, column))

        return results

    def _validate_column_generic(self, series: pd.Series, column: str) -> List[ValidationResult]:
        """通用列验证"""
        results = []

        # 根据列名推断数据类型并验证
        column_lower = column.lower()

        # 价格相关字段验证
        if any(keyword in column_lower for keyword in ['price', 'open', 'high', 'low', 'close']):
            results.extend(self._validate_price_column(series, column))

        # 成交量相关字段验证
        elif any(keyword in column_lower for keyword in ['volume', 'amount']):
            results.extend(self._validate_volume_column(series, column))

        # 股票代码验证
        elif 'symbol' in column_lower or 'code' in column_lower:
            results.extend(self._validate_symbol_column(series, column))

        return results

    def _validate_price_column(self, series: pd.Series, column: str) -> List[ValidationResult]:
        """验证价格列"""
        results = []

        # 检查负值
        negative_count = (series < 0).sum()
        if negative_count > 0:
            results.append(ValidationResult(
                field=column,
                level=ValidationLevel.ERROR,
                message="价格不能为负值",
                count=negative_count,
                percentage=(negative_count / len(series)) * 100
            ))

        # 检查零值
        zero_count = (series == 0).sum()
        if zero_count > 0:
            results.append(ValidationResult(
                field=column,
                level=ValidationLevel.WARNING,
                message="价格为零",
                count=zero_count,
                percentage=(zero_count / len(series)) * 100
            ))

        return results

    def _validate_volume_column(self, series: pd.Series, column: str) -> List[ValidationResult]:
        """验证成交量列"""
        results = []

        # 检查负值
        negative_count = (series < 0).sum()
        if negative_count > 0:
            results.append(ValidationResult(
                field=column,
                level=ValidationLevel.ERROR,
                message="成交量不能为负值",
                count=negative_count,
                percentage=(negative_count / len(series)) * 100
            ))

        return results

    def _validate_symbol_column(self, series: pd.Series, column: str) -> List[ValidationResult]:
        """验证股票代码列"""
        results = []

        # 检查股票代码格式
        valid_patterns = [
            r'^\d{6}\.(SZ|SH)$',  # A股格式：000001.SZ
            r'^[A-Z]{1,5}$',      # 美股格式：AAPL
            r'^\d{4}\.HK$'        # 港股格式：0700.HK
        ]

        invalid_count = 0
        for value in series.dropna():
            if not any(re.match(pattern, str(value)) for pattern in valid_patterns):
                invalid_count += 1

        if invalid_count > 0:
            results.append(ValidationResult(
                field=column,
                level=ValidationLevel.WARNING,
                message="股票代码格式不规范",
                count=invalid_count,
                percentage=(invalid_count / len(series)) * 100
            ))

        return results

    def _validate_business_rules(self, df: pd.DataFrame, schema: Dict[str, Any]) -> List[ValidationResult]:
        """验证业务规则"""
        results = []

        # 价格逻辑关系验证
        if all(col in df.columns for col in ['open', 'high', 'low', 'close']):
            # high >= max(open, close) and low <= min(open, close)
            invalid_high = (df['high'] < df[['open', 'close']].max(axis=1)).sum()
            invalid_low = (df['low'] > df[['open', 'close']].min(axis=1)).sum()

            if invalid_high > 0:
                results.append(ValidationResult(
                    field="high",
                    level=ValidationLevel.ERROR,
                    message="最高价小于开盘价或收盘价",
                    count=invalid_high,
                    percentage=(invalid_high / len(df)) * 100
                ))

            if invalid_low > 0:
                results.append(ValidationResult(
                    field="low",
                    level=ValidationLevel.ERROR,
                    message="最低价大于开盘价或收盘价",
                    count=invalid_low,
                    percentage=(invalid_low / len(df)) * 100
                ))

        return results

    def calculate_quality_score(self, validation_results: List[ValidationResult]) -> Dict[str, float]:
        """计算数据质量评分"""
        scores = {
            'completeness': 1.0,
            'accuracy': 1.0,
            'consistency': 1.0,
            'validity': 1.0,
            'overall': 1.0
        }

        total_issues = len(validation_results)
        if total_issues == 0:
            return scores

        # 根据问题级别计算扣分
        deductions = {
            ValidationLevel.INFO: 0.01,
            ValidationLevel.WARNING: 0.05,
            ValidationLevel.ERROR: 0.1,
            ValidationLevel.CRITICAL: 0.2
        }

        for result in validation_results:
            deduction = deductions.get(result.level, 0.1)

            # 根据问题类型影响不同的质量维度
            if 'null' in result.message.lower() or 'empty' in result.message.lower():
                scores['completeness'] -= deduction
            elif 'invalid' in result.message.lower() or 'format' in result.message.lower():
                scores['validity'] -= deduction
            elif 'inconsistent' in result.message.lower() or 'type' in result.message.lower():
                scores['consistency'] -= deduction
            else:
                scores['accuracy'] -= deduction

        # 确保分数不低于0
        for key in scores:
            scores[key] = max(0.0, scores[key])

        # 计算总体评分
        scores['overall'] = np.mean([scores['completeness'], scores['accuracy'],
                                   scores['consistency'], scores['validity']])

        return scores

    def generate_quality_report(self, df: pd.DataFrame, validation_results: List[ValidationResult]) -> Dict[str, Any]:
        """生成数据质量报告"""
        quality_scores = self.calculate_quality_score(validation_results)

        # 按级别统计问题
        issue_counts = {level.value: 0 for level in ValidationLevel}
        for result in validation_results:
            issue_counts[result.level.value] += 1

        report = {
            'summary': {
                'total_rows': len(df),
                'total_columns': len(df.columns),
                'total_issues': len(validation_results),
                'quality_scores': quality_scores
            },
            'issue_distribution': issue_counts,
            'generated_at': datetime.now().isoformat()
        }

        return report


# 全局数据验证器实例
data_validator = DataValidator()


def validate_market_data(df: pd.DataFrame) -> List[ValidationResult]:
    """验证市场数据的便捷函数"""
    return data_validator.validate_dataframe(df)


def calculate_data_quality_score(validation_results: List[ValidationResult]) -> Dict[str, float]:
    """计算数据质量评分的便捷函数"""
    return data_validator.calculate_quality_score(validation_results)