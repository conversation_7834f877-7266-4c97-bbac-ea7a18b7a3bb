"""
数据适配器模块

提供统一的数据接口，支持多种数据源：
- Tushare数据源
- Yahoo Finance数据源
- Wind数据源（占位符）
- 东方财富数据源（占位符）
"""

import logging
import pandas as pd
from abc import ABC, abstractmethod
from typing import Dict, List, Optional, Any
from datetime import datetime, date

logger = logging.getLogger(__name__)


class DataAdapter(ABC):
    """数据适配器基类"""

    def __init__(self, name: str, config: Dict[str, Any] = None):
        self.name = name
        self.config = config or {}
        self._initialized = False

    @abstractmethod
    def initialize(self) -> bool:
        """初始化数据源连接"""
        pass

    @abstractmethod
    def get_stock_list(self) -> pd.DataFrame:
        """获取股票列表"""
        pass

    @abstractmethod
    def get_daily_data(self, symbol: str, start_date: date, end_date: date) -> pd.DataFrame:
        """获取日线数据"""
        pass

    @abstractmethod
    def get_realtime_data(self, symbols: List[str]) -> pd.DataFrame:
        """获取实时数据"""
        pass

    def is_available(self) -> bool:
        """检查数据源是否可用"""
        return self._initialized

    def close(self):
        """关闭数据源连接"""
        self._initialized = False
        logger.info(f"数据源 {self.name} 连接已关闭")


class TushareAdapter(DataAdapter):
    """Tushare数据适配器"""

    def __init__(self, token: str = None):
        super().__init__("Tushare")
        self.token = token
        self._client = None

    def initialize(self) -> bool:
        """初始化Tushare连接"""
        try:
            # 模拟初始化成功
            self._initialized = True
            logger.info("Tushare数据源初始化成功（模拟模式）")
            return True
        except Exception as e:
            logger.error(f"Tushare初始化失败: {e}")
            return False

    def get_stock_list(self) -> pd.DataFrame:
        """获取股票列表"""
        if not self._initialized:
            self.initialize()

        # 返回模拟数据
        df = pd.DataFrame({
            'ts_code': ['000001.SZ', '000002.SZ', '600000.SH', '600036.SH', '000858.SZ'],
            'symbol': ['000001', '000002', '600000', '600036', '000858'],
            'name': ['平安银行', '万科A', '浦发银行', '招商银行', '五粮液'],
            'area': ['深圳', '深圳', '上海', '上海', '四川'],
            'industry': ['银行', '房地产', '银行', '银行', '白酒'],
            'market': ['主板', '主板', '主板', '主板', '主板']
        })

        logger.info(f"获取股票列表成功，共 {len(df)} 只股票")
        return df

    def get_daily_data(self, symbol: str, start_date: date, end_date: date) -> pd.DataFrame:
        """获取日线数据"""
        if not self._initialized:
            self.initialize()

        try:
            # 生成模拟数据
            import numpy as np
            dates = pd.date_range(start=start_date, end=end_date, freq='D')
            dates = dates[dates.weekday < 5]  # 只保留工作日

            # 生成模拟价格数据
            np.random.seed(42)
            base_price = 10.0
            prices = []
            for i in range(len(dates)):
                if i == 0:
                    price = base_price
                else:
                    change = np.random.normal(0, 0.02)  # 2%的日波动率
                    price = prices[-1] * (1 + change)
                prices.append(max(price, 0.1))  # 确保价格为正

            df = pd.DataFrame({
                'ts_code': [symbol] * len(dates),
                'trade_date': dates.strftime('%Y%m%d'),
                'open': [round(p * (1 + np.random.normal(0, 0.005)), 2) for p in prices],
                'high': [round(p * (1 + abs(np.random.normal(0, 0.01))), 2) for p in prices],
                'low': [round(p * (1 - abs(np.random.normal(0, 0.01))), 2) for p in prices],
                'close': [round(p, 2) for p in prices],
                'vol': [np.random.randint(1000000, 10000000) for _ in prices],
                'amount': [round(p * v / 100, 2) for p, v in zip(prices, [np.random.randint(1000000, 10000000) for _ in prices])]
            })

            logger.info(f"获取 {symbol} 日线数据成功，共 {len(df)} 条记录")
            return df

        except Exception as e:
            logger.error(f"获取日线数据失败: {e}")
            return pd.DataFrame()

    def get_realtime_data(self, symbols: List[str]) -> pd.DataFrame:
        """获取实时数据"""
        if not self._initialized:
            self.initialize()

        try:
            # 模拟实时数据
            import numpy as np
            np.random.seed(int(datetime.now().timestamp()))

            data = []
            for symbol in symbols:
                base_price = 10.0 + np.random.random() * 90  # 10-100的随机基础价格
                change = np.random.normal(0, 0.5)
                data.append({
                    'symbol': symbol,
                    'price': round(base_price, 2),
                    'change': round(change, 2),
                    'pct_change': round((change / base_price) * 100, 2),
                    'volume': np.random.randint(100000, 5000000),
                    'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                })

            df = pd.DataFrame(data)
            logger.info(f"获取实时数据成功，共 {len(symbols)} 只股票")
            return df

        except Exception as e:
            logger.error(f"获取实时数据失败: {e}")
            return pd.DataFrame()


class YahooFinanceAdapter(DataAdapter):
    """Yahoo Finance数据适配器"""

    def __init__(self):
        super().__init__("Yahoo Finance")

    def initialize(self) -> bool:
        """初始化Yahoo Finance连接"""
        try:
            self._initialized = True
            logger.info("Yahoo Finance数据源初始化成功（模拟模式）")
            return True
        except Exception as e:
            logger.error(f"Yahoo Finance初始化失败: {e}")
            return False

    def get_stock_list(self) -> pd.DataFrame:
        """获取股票列表（美股示例）"""
        stocks = [
            {'symbol': 'AAPL', 'name': 'Apple Inc.', 'exchange': 'NASDAQ'},
            {'symbol': 'GOOGL', 'name': 'Alphabet Inc.', 'exchange': 'NASDAQ'},
            {'symbol': 'MSFT', 'name': 'Microsoft Corporation', 'exchange': 'NASDAQ'},
            {'symbol': 'TSLA', 'name': 'Tesla, Inc.', 'exchange': 'NASDAQ'},
            {'symbol': 'AMZN', 'name': 'Amazon.com, Inc.', 'exchange': 'NASDAQ'}
        ]

        df = pd.DataFrame(stocks)
        logger.info(f"获取美股列表成功，共 {len(df)} 只股票")
        return df

    def get_daily_data(self, symbol: str, start_date: date, end_date: date) -> pd.DataFrame:
        """获取日线数据"""
        if not self._initialized:
            self.initialize()

        try:
            # 模拟数据
            dates = pd.date_range(start=start_date, end=end_date, freq='D')
            dates = dates[dates.weekday < 5]  # 只保留工作日

            import numpy as np
            np.random.seed(42)
            base_price = 150.0
            prices = []
            for i in range(len(dates)):
                if i == 0:
                    price = base_price
                else:
                    change = np.random.normal(0, 0.02)
                    price = prices[-1] * (1 + change)
                prices.append(max(price, 1.0))

            df = pd.DataFrame({
                'Date': dates,
                'Open': [round(p * (1 + np.random.normal(0, 0.005)), 2) for p in prices],
                'High': [round(p * (1 + abs(np.random.normal(0, 0.01))), 2) for p in prices],
                'Low': [round(p * (1 - abs(np.random.normal(0, 0.01))), 2) for p in prices],
                'Close': [round(p, 2) for p in prices],
                'Volume': [np.random.randint(1000000, 50000000) for _ in prices],
                'symbol': [symbol] * len(dates)
            })

            logger.info(f"获取 {symbol} 日线数据成功，共 {len(df)} 条记录")
            return df

        except Exception as e:
            logger.error(f"获取日线数据失败: {e}")
            return pd.DataFrame()

    def get_realtime_data(self, symbols: List[str]) -> pd.DataFrame:
        """获取实时数据"""
        if not self._initialized:
            self.initialize()

        import numpy as np
        np.random.seed(int(datetime.now().timestamp()))

        data = []
        for symbol in symbols:
            base_price = 100.0 + np.random.random() * 400  # 100-500的随机基础价格
            change = np.random.normal(0, 2)
            data.append({
                'symbol': symbol,
                'price': round(base_price, 2),
                'change': round(change, 2),
                'pct_change': round((change / base_price) * 100, 2),
                'volume': np.random.randint(1000000, 20000000),
                'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            })

        df = pd.DataFrame(data)
        logger.info(f"获取美股实时数据成功，共 {len(symbols)} 只股票")
        return df