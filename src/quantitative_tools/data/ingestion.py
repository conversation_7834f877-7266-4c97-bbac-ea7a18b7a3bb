"""
数据接入引擎

负责协调多个数据源，提供统一的数据接入接口，包括：
- 数据源管理和故障切换
- 数据清洗和验证
- 数据缓存和存储
- 实时数据推送
"""

import logging
import asyncio
import pandas as pd
from typing import Dict, List, Optional, Any, Callable
from datetime import datetime, date, timedelta
from concurrent.futures import ThreadPoolExecutor, as_completed

from .adapters import DataAdapter, TushareAdapter, YahooFinanceAdapter

logger = logging.getLogger(__name__)


class DataIngestionEngine:
    """数据接入引擎"""

    def __init__(self):
        self._adapters: Dict[str, DataAdapter] = {}
        self._primary_adapter: Optional[str] = None
        self._fallback_adapters: List[str] = []
        self._cache: Dict[str, Any] = {}
        self._subscribers: Dict[str, List[Callable]] = {}
        self._running = False
        self._executor = ThreadPoolExecutor(max_workers=4)

    def register_adapter(self, adapter: DataAdapter, is_primary: bool = False):
        """注册数据适配器"""
        self._adapters[adapter.name] = adapter

        if is_primary:
            self._primary_adapter = adapter.name
        else:
            if adapter.name not in self._fallback_adapters:
                self._fallback_adapters.append(adapter.name)

        logger.info(f"注册数据适配器: {adapter.name} ({'主要' if is_primary else '备用'})")

    def initialize(self):
        """初始化所有数据适配器"""
        success_count = 0

        for name, adapter in self._adapters.items():
            try:
                if adapter.initialize():
                    success_count += 1
                    logger.info(f"数据适配器 {name} 初始化成功")
                else:
                    logger.warning(f"数据适配器 {name} 初始化失败")
            except Exception as e:
                logger.error(f"数据适配器 {name} 初始化异常: {e}")

        logger.info(f"数据接入引擎初始化完成，成功初始化 {success_count}/{len(self._adapters)} 个适配器")
        return success_count > 0

    def get_available_adapter(self, preferred: str = None) -> Optional[DataAdapter]:
        """获取可用的数据适配器"""
        # 优先使用指定的适配器
        if preferred and preferred in self._adapters:
            adapter = self._adapters[preferred]
            if adapter.is_available():
                return adapter

        # 使用主要适配器
        if self._primary_adapter and self._primary_adapter in self._adapters:
            adapter = self._adapters[self._primary_adapter]
            if adapter.is_available():
                return adapter

        # 使用备用适配器
        for adapter_name in self._fallback_adapters:
            if adapter_name in self._adapters:
                adapter = self._adapters[adapter_name]
                if adapter.is_available():
                    logger.warning(f"主要数据源不可用，切换到备用数据源: {adapter_name}")
                    return adapter

        logger.error("没有可用的数据适配器")
        return None

    def get_stock_list(self, source: str = None) -> pd.DataFrame:
        """获取股票列表"""
        adapter = self.get_available_adapter(source)
        if not adapter:
            return pd.DataFrame()

        try:
            cache_key = f"stock_list_{adapter.name}"

            # 检查缓存
            if cache_key in self._cache:
                cache_time, data = self._cache[cache_key]
                if datetime.now() - cache_time < timedelta(hours=1):  # 缓存1小时
                    logger.debug(f"从缓存获取股票列表: {adapter.name}")
                    return data

            # 从数据源获取
            data = adapter.get_stock_list()

            # 更新缓存
            self._cache[cache_key] = (datetime.now(), data)

            return data

        except Exception as e:
            logger.error(f"获取股票列表失败: {e}")
            return pd.DataFrame()

    def get_daily_data(self, symbol: str, start_date: date, end_date: date, source: str = None) -> pd.DataFrame:
        """获取日线数据"""
        adapter = self.get_available_adapter(source)
        if not adapter:
            return pd.DataFrame()

        try:
            cache_key = f"daily_{symbol}_{start_date}_{end_date}_{adapter.name}"

            # 检查缓存
            if cache_key in self._cache:
                cache_time, data = self._cache[cache_key]
                if datetime.now() - cache_time < timedelta(minutes=30):  # 缓存30分钟
                    logger.debug(f"从缓存获取日线数据: {symbol}")
                    return data

            # 从数据源获取
            data = adapter.get_daily_data(symbol, start_date, end_date)

            # 数据清洗和验证
            if not data.empty:
                data = self._clean_daily_data(data)

            # 更新缓存
            self._cache[cache_key] = (datetime.now(), data)

            return data

        except Exception as e:
            logger.error(f"获取日线数据失败 {symbol}: {e}")
            return pd.DataFrame()

    def get_realtime_data(self, symbols: List[str], source: str = None) -> pd.DataFrame:
        """获取实时数据"""
        adapter = self.get_available_adapter(source)
        if not adapter:
            return pd.DataFrame()

        try:
            data = adapter.get_realtime_data(symbols)

            # 数据清洗和验证
            if not data.empty:
                data = self._clean_realtime_data(data)

                # 通知订阅者
                self._notify_subscribers('realtime_data', data)

            return data

        except Exception as e:
            logger.error(f"获取实时数据失败: {e}")
            return pd.DataFrame()

    def _clean_daily_data(self, data: pd.DataFrame) -> pd.DataFrame:
        """清洗日线数据"""
        if data.empty:
            return data

        try:
            # 移除空值
            data = data.dropna()

            # 确保价格为正数
            price_columns = ['open', 'high', 'low', 'close', 'Open', 'High', 'Low', 'Close']
            for col in price_columns:
                if col in data.columns:
                    data = data[data[col] > 0]

            logger.debug(f"数据清洗完成，剩余 {len(data)} 条记录")
            return data

        except Exception as e:
            logger.error(f"数据清洗失败: {e}")
            return data

    def _clean_realtime_data(self, data: pd.DataFrame) -> pd.DataFrame:
        """清洗实时数据"""
        if data.empty:
            return data

        try:
            # 移除空值
            data = data.dropna(subset=['symbol', 'price'])

            # 确保价格为正数
            data = data[data['price'] > 0]

            logger.debug(f"实时数据清洗完成，剩余 {len(data)} 条记录")
            return data

        except Exception as e:
            logger.error(f"实时数据清洗失败: {e}")
            return data

    def _notify_subscribers(self, event_type: str, data: Any):
        """通知订阅者"""
        if event_type in self._subscribers:
            for callback in self._subscribers[event_type]:
                try:
                    callback(data)
                except Exception as e:
                    logger.error(f"通知订阅者失败: {e}")

    def subscribe_realtime_data(self, callback: Callable[[pd.DataFrame], None]):
        """订阅实时数据推送"""
        if 'realtime_data' not in self._subscribers:
            self._subscribers['realtime_data'] = []

        self._subscribers['realtime_data'].append(callback)
        logger.info("添加实时数据订阅者")

    def get_cache_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        return {
            "cache_size": len(self._cache),
            "adapters_count": len(self._adapters),
            "available_adapters": [name for name, adapter in self._adapters.items() if adapter.is_available()],
            "subscribers_count": sum(len(callbacks) for callbacks in self._subscribers.values())
        }

    def clear_cache(self):
        """清空缓存"""
        self._cache.clear()
        logger.info("缓存已清空")

    def close(self):
        """关闭数据接入引擎"""
        for adapter in self._adapters.values():
            adapter.close()

        self._executor.shutdown(wait=True)
        logger.info("数据接入引擎已关闭")