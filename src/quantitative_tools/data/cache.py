"""
多级缓存架构

提供高性能的多级缓存系统，包括：
- L1缓存：内存缓存（最快访问）
- L2缓存：Redis缓存（中等速度）
- L3缓存：本地文件缓存（较慢但持久）
- 智能缓存策略和失效机制
"""

import logging
import pickle
import hashlib
import pandas as pd
from typing import Any, Optional, Dict
from datetime import datetime, timedelta
import os
import threading
from pathlib import Path

logger = logging.getLogger(__name__)

try:
    import redis
    REDIS_AVAILABLE = True
except ImportError:
    redis = None
    REDIS_AVAILABLE = False


class MultiLevelCache:
    """多级缓存管理器"""

    def __init__(self,
                 redis_host: str = "localhost",
                 redis_port: int = 6379,
                 redis_db: int = 0,
                 local_cache_dir: str = "./cache"):

        # L1缓存：内存缓存
        self._memory_cache: Dict[str, Dict[str, Any]] = {}
        self._memory_cache_lock = threading.RLock()
        self._memory_cache_max_size = 1000

        # L2缓存：Redis缓存
        self._redis_client = None
        self._redis_config = {
            'host': redis_host,
            'port': redis_port,
            'db': redis_db
        }

        # L3缓存：本地文件缓存
        self._local_cache_dir = Path(local_cache_dir)
        self._local_cache_dir.mkdir(parents=True, exist_ok=True)

        # 缓存配置
        self._cache_config = {
            'default_ttl': 3600,  # 1小时
            'memory_ttl': 300,    # 5分钟
            'redis_ttl': 3600,    # 1小时
            'file_ttl': 86400,    # 24小时
        }

        self._initialized = False

    def initialize(self):
        """初始化缓存系统"""
        try:
            # 初始化Redis连接
            if REDIS_AVAILABLE:
                self._redis_client = redis.Redis(**self._redis_config, decode_responses=False)
                self._redis_client.ping()
                logger.info("Redis缓存初始化成功")
            else:
                logger.warning("Redis不可用，跳过L2缓存")

            self._initialized = True
            logger.info("多级缓存系统初始化完成")
            return True

        except Exception as e:
            logger.error(f"缓存系统初始化失败: {e}")
            self._initialized = True  # 继续使用其他缓存层
            return True

    def get(self, key: str, default: Any = None) -> Any:
        """从缓存获取数据（按L1->L2->L3顺序查找）"""
        if not self._initialized:
            self.initialize()

        # L1缓存：内存缓存
        value = self._get_from_memory(key)
        if value is not None:
            logger.debug(f"从L1缓存命中: {key}")
            return value

        # L2缓存：Redis缓存
        value = self._get_from_redis(key)
        if value is not None:
            logger.debug(f"从L2缓存命中: {key}")
            # 回写到L1缓存
            self._set_to_memory(key, value, self._cache_config['memory_ttl'])
            return value

        # L3缓存：文件缓存
        value = self._get_from_file(key)
        if value is not None:
            logger.debug(f"从L3缓存命中: {key}")
            # 回写到L1和L2缓存
            self._set_to_memory(key, value, self._cache_config['memory_ttl'])
            self._set_to_redis(key, value, self._cache_config['redis_ttl'])
            return value

        logger.debug(f"缓存未命中: {key}")
        return default

    def set(self, key: str, value: Any, ttl: int = None) -> bool:
        """设置缓存数据（写入所有缓存层）"""
        if not self._initialized:
            self.initialize()

        ttl = ttl or self._cache_config['default_ttl']

        success = True

        # 写入L1缓存
        if not self._set_to_memory(key, value, min(ttl, self._cache_config['memory_ttl'])):
            success = False

        # 写入L2缓存
        if not self._set_to_redis(key, value, min(ttl, self._cache_config['redis_ttl'])):
            success = False

        # 写入L3缓存
        if not self._set_to_file(key, value, min(ttl, self._cache_config['file_ttl'])):
            success = False

        if success:
            logger.debug(f"缓存设置成功: {key}")
        else:
            logger.warning(f"缓存设置部分失败: {key}")

        return success

    def _get_from_memory(self, key: str) -> Any:
        """从内存缓存获取数据"""
        try:
            with self._memory_cache_lock:
                if key in self._memory_cache:
                    cache_item = self._memory_cache[key]

                    # 检查是否过期
                    if datetime.now() < cache_item['expires_at']:
                        return cache_item['value']
                    else:
                        # 删除过期数据
                        del self._memory_cache[key]

        except Exception as e:
            logger.error(f"从内存缓存获取数据失败 {key}: {e}")

        return None

    def _set_to_memory(self, key: str, value: Any, ttl: int) -> bool:
        """设置内存缓存"""
        try:
            with self._memory_cache_lock:
                # 检查缓存大小限制
                if len(self._memory_cache) >= self._memory_cache_max_size:
                    self._evict_memory_cache()

                expires_at = datetime.now() + timedelta(seconds=ttl)
                self._memory_cache[key] = {
                    'value': value,
                    'expires_at': expires_at,
                    'created_at': datetime.now()
                }

            return True

        except Exception as e:
            logger.error(f"设置内存缓存失败 {key}: {e}")
            return False

    def _evict_memory_cache(self):
        """清理内存缓存（LRU策略）"""
        try:
            # 按创建时间排序，删除最旧的25%
            items = list(self._memory_cache.items())
            items.sort(key=lambda x: x[1]['created_at'])

            evict_count = len(items) // 4
            for i in range(evict_count):
                key = items[i][0]
                del self._memory_cache[key]

            logger.debug(f"内存缓存清理完成，删除 {evict_count} 个项目")

        except Exception as e:
            logger.error(f"内存缓存清理失败: {e}")

    def _get_from_redis(self, key: str) -> Any:
        """从Redis缓存获取数据"""
        if not self._redis_client:
            return None

        try:
            data = self._redis_client.get(key)
            if data:
                return pickle.loads(data)
        except Exception as e:
            logger.error(f"从Redis缓存获取数据失败 {key}: {e}")

        return None

    def _set_to_redis(self, key: str, value: Any, ttl: int) -> bool:
        """设置Redis缓存"""
        if not self._redis_client:
            return True  # 如果Redis不可用，不算失败

        try:
            data = pickle.dumps(value)
            self._redis_client.setex(key, ttl, data)
            return True
        except Exception as e:
            logger.error(f"设置Redis缓存失败 {key}: {e}")
            return False

    def _get_from_file(self, key: str) -> Any:
        """从文件缓存获取数据"""
        try:
            file_path = self._get_cache_file_path(key)
            if file_path.exists():
                with open(file_path, 'rb') as f:
                    cache_data = pickle.load(f)

                # 检查是否过期
                if datetime.now() < cache_data['expires_at']:
                    return cache_data['value']
                else:
                    # 删除过期文件
                    file_path.unlink()

        except Exception as e:
            logger.error(f"从文件缓存获取数据失败 {key}: {e}")

        return None

    def _set_to_file(self, key: str, value: Any, ttl: int) -> bool:
        """设置文件缓存"""
        try:
            file_path = self._get_cache_file_path(key)
            file_path.parent.mkdir(parents=True, exist_ok=True)

            cache_data = {
                'value': value,
                'expires_at': datetime.now() + timedelta(seconds=ttl),
                'created_at': datetime.now()
            }

            with open(file_path, 'wb') as f:
                pickle.dump(cache_data, f)

            return True

        except Exception as e:
            logger.error(f"设置文件缓存失败 {key}: {e}")
            return False

    def _get_cache_file_path(self, key: str) -> Path:
        """获取缓存文件路径"""
        # 使用MD5哈希避免文件名过长或包含特殊字符
        key_hash = hashlib.md5(key.encode()).hexdigest()
        return self._local_cache_dir / f"{key_hash}.cache"

    def get_cache_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        stats = {
            'memory_cache': {
                'size': len(self._memory_cache),
                'max_size': self._memory_cache_max_size
            },
            'redis_cache': {
                'available': self._redis_client is not None
            },
            'file_cache': {
                'directory': str(self._local_cache_dir),
                'files_count': len(list(self._local_cache_dir.glob('*.cache')))
            }
        }

        return stats

    def clear_all_cache(self):
        """清空所有缓存"""
        # 清空内存缓存
        with self._memory_cache_lock:
            self._memory_cache.clear()

        # 清空Redis缓存
        if self._redis_client:
            try:
                self._redis_client.flushdb()
            except Exception as e:
                logger.error(f"清空Redis缓存失败: {e}")

        # 清空文件缓存
        try:
            for cache_file in self._local_cache_dir.glob('*.cache'):
                cache_file.unlink()
        except Exception as e:
            logger.error(f"清空文件缓存失败: {e}")

        logger.info("所有缓存已清空")


# 全局缓存实例
cache_manager = MultiLevelCache()


def get_cached_data(key: str, default: Any = None) -> Any:
    """获取缓存数据的便捷函数"""
    return cache_manager.get(key, default)


def set_cached_data(key: str, value: Any, ttl: int = None) -> bool:
    """设置缓存数据的便捷函数"""
    return cache_manager.set(key, value, ttl)