"""
用户相关数据模型

包括用户、角色和权限管理相关的数据模型。
"""

from typing import Optional, List
from datetime import datetime

try:
    from sqlalchemy import Column, String, Boolean, Integer, DateTime, Text, ForeignKey, Table
    from sqlalchemy.orm import relationship
    from sqlalchemy.dialects.postgresql import UUID, JSONB
    SQLALCHEMY_AVAILABLE = True
except ImportError:
    Column = String = Boolean = Integer = DateTime = Text = ForeignKey = Table = None
    relationship = lambda *args, **kwargs: None
    UUID = JSONB = None
    SQLALCHEMY_AVAILABLE = False

from .base import BaseModel


class User(BaseModel):
    """用户模型"""

    if SQLALCHEMY_AVAILABLE:
        __tablename__ = 'users'

        username = Column(String(50), unique=True, nullable=False, comment="用户名")
        email = Column(String(100), unique=True, nullable=False, comment="邮箱地址")
        password_hash = Column(String(255), nullable=False, comment="密码哈希")
        full_name = Column(String(100), comment="全名")
        phone = Column(String(20), comment="手机号")
        is_active = Column(Boolean, default=True, comment="是否激活")
        is_verified = Column(Boolean, default=False, comment="是否已验证")
        is_superuser = Column(Boolean, default=False, comment="是否超级用户")
        last_login = Column(DateTime, comment="最后登录时间")
        login_attempts = Column(Integer, default=0, comment="登录尝试次数")
        locked_until = Column(DateTime, comment="锁定到期时间")

    def __repr__(self):
        return f"<User(username='{getattr(self, 'username', 'N/A')}')>"

    def has_permission(self, permission: str) -> bool:
        """检查用户是否具有指定权限"""
        # 简化实现，实际应用中需要检查角色权限
        return getattr(self, 'is_superuser', False)


class Role(BaseModel):
    """角色模型"""

    if SQLALCHEMY_AVAILABLE:
        __tablename__ = 'roles'

        name = Column(String(50), unique=True, nullable=False, comment="角色名称")
        description = Column(Text, comment="角色描述")
        permissions = Column(JSONB, default=list, comment="权限列表")

    def __repr__(self):
        return f"<Role(name='{getattr(self, 'name', 'N/A')}')>"


# 权限常量
class Permissions:
    """权限常量定义"""

    # 用户管理权限
    USER_READ = "user:read"
    USER_WRITE = "user:write"
    USER_DELETE = "user:delete"

    # 策略管理权限
    STRATEGY_READ = "strategy:read"
    STRATEGY_WRITE = "strategy:write"
    STRATEGY_DELETE = "strategy:delete"
    STRATEGY_EXECUTE = "strategy:execute"

    # 交易权限
    ORDER_READ = "order:read"
    ORDER_WRITE = "order:write"
    ORDER_DELETE = "order:delete"

    # 持仓权限
    POSITION_READ = "position:read"
    POSITION_WRITE = "position:write"

    # 回测权限
    BACKTEST_READ = "backtest:read"
    BACKTEST_WRITE = "backtest:write"
    BACKTEST_DELETE = "backtest:delete"

    # 风险管理权限
    RISK_READ = "risk:read"
    RISK_WRITE = "risk:write"
    RISK_DELETE = "risk:delete"

    # 系统管理权限
    SYSTEM_READ = "system:read"
    SYSTEM_WRITE = "system:write"
    SYSTEM_DELETE = "system:delete"

    # 超级权限
    ALL = "*"


# 默认角色定义
DEFAULT_ROLES = [
    {
        "name": "admin",
        "description": "系统管理员",
        "permissions": [Permissions.ALL]
    },
    {
        "name": "trader",
        "description": "交易员",
        "permissions": [
            Permissions.STRATEGY_READ,
            Permissions.STRATEGY_WRITE,
            Permissions.ORDER_READ,
            Permissions.ORDER_WRITE,
            Permissions.POSITION_READ,
            Permissions.BACKTEST_READ,
            Permissions.BACKTEST_WRITE
        ]
    },
    {
        "name": "viewer",
        "description": "观察者",
        "permissions": [
            Permissions.STRATEGY_READ,
            Permissions.ORDER_READ,
            Permissions.POSITION_READ,
            Permissions.BACKTEST_READ,
            Permissions.RISK_READ
        ]
    }
]