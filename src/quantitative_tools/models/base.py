"""
基础数据模型

定义所有数据模型的基类和通用字段。
"""

import uuid
from datetime import datetime
from typing import Any

try:
    from sqlalchemy import Column, String, DateTime, Boolean
    from sqlalchemy.ext.declarative import declarative_base
    from sqlalchemy.dialects.postgresql import UUID
    SQLALCHEMY_AVAILABLE = True
except ImportError:
    # 如果SQLAlchemy不可用，创建占位符
    Column = None
    String = None
    DateTime = None
    Boolean = None
    UUID = None
    declarative_base = lambda: type('Base', (), {})
    SQLALCHEMY_AVAILABLE = False

# 创建基础模型类
if SQLALCHEMY_AVAILABLE:
    Base = declarative_base()
else:
    Base = type('Base', (), {})


class BaseModel:
    """基础模型类

    包含所有模型的通用字段：
    - id: 主键UUID
    - created_at: 创建时间
    - updated_at: 更新时间
    - is_deleted: 软删除标记
    """

    if SQLALCHEMY_AVAILABLE:
        __abstract__ = True

        id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, comment="主键ID")
        created_at = Column(DateTime, default=datetime.utcnow, comment="创建时间")
        updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, comment="更新时间")
        is_deleted = Column(Boolean, default=False, comment="软删除标记")

    def to_dict(self) -> dict:
        """转换为字典"""
        if SQLALCHEMY_AVAILABLE:
            return {
                column.name: getattr(self, column.name)
                for column in self.__table__.columns
            }
        else:
            return {}

    def __repr__(self):
        """字符串表示"""
        if hasattr(self, 'id'):
            return f"<{self.__class__.__name__}(id={self.id})>"
        else:
            return f"<{self.__class__.__name__}>"


# 如果SQLAlchemy可用，让BaseModel继承Base
if SQLALCHEMY_AVAILABLE:
    class BaseModel(Base, BaseModel):
        __abstract__ = True