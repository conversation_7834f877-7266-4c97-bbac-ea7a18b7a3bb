"""
策略执行引擎

负责策略的调度、执行和监控，包括：
- 多策略并发执行
- 策略生命周期管理
- 性能监控和统计
- 异常处理和恢复
"""

import logging
from typing import Dict, List, Optional, Any
from datetime import datetime
from concurrent.futures import ThreadPoolExecutor

from .base import BaseStrategy, StrategyState

logger = logging.getLogger(__name__)


class StrategyEngine:
    """策略执行引擎"""

    def __init__(self, max_workers: int = 4):
        self._strategies: Dict[str, BaseStrategy] = {}
        self._executor = ThreadPoolExecutor(max_workers=max_workers)
        self._running = False

    def register_strategy(self, strategy: BaseStrategy) -> bool:
        """注册策略"""
        try:
            strategy_id = strategy.context.strategy_id
            self._strategies[strategy_id] = strategy
            logger.info(f"策略注册成功: {strategy.context.name}")
            return True
        except Exception as e:
            logger.error(f"策略注册失败: {e}")
            return False

    def start_strategy(self, strategy_id: str) -> bool:
        """启动指定策略"""
        if strategy_id not in self._strategies:
            logger.error(f"策略不存在: {strategy_id}")
            return False

        strategy = self._strategies[strategy_id]
        return strategy.start()

    def stop_strategy(self, strategy_id: str) -> bool:
        """停止指定策略"""
        if strategy_id not in self._strategies:
            logger.error(f"策略不存在: {strategy_id}")
            return False

        strategy = self._strategies[strategy_id]
        return strategy.stop()

    def get_strategy_status(self, strategy_id: str) -> Optional[Dict[str, Any]]:
        """获取策略状态"""
        if strategy_id not in self._strategies:
            return None

        strategy = self._strategies[strategy_id]
        return strategy.context.to_dict()

    def list_strategies(self) -> List[Dict[str, Any]]:
        """列出所有策略"""
        return [strategy.context.to_dict() for strategy in self._strategies.values()]

    def start_engine(self) -> bool:
        """启动引擎"""
        try:
            self._running = True
            logger.info("策略引擎启动成功")
            return True
        except Exception as e:
            logger.error(f"策略引擎启动失败: {e}")
            return False

    def stop_engine(self) -> bool:
        """停止引擎"""
        try:
            self._running = False

            # 停止所有策略
            for strategy in self._strategies.values():
                if strategy.context.state == StrategyState.RUNNING:
                    strategy.stop()

            # 关闭线程池
            self._executor.shutdown(wait=True)

            logger.info("策略引擎停止")
            return True
        except Exception as e:
            logger.error(f"策略引擎停止失败: {e}")
            return False


# 全局策略引擎实例
strategy_engine = StrategyEngine()