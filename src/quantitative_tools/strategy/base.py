"""
策略基础框架

定义策略的基础接口和抽象类，包括：
- 策略基类和生命周期管理
- 策略上下文和状态管理
- 策略参数和配置
- 策略性能指标计算
"""

import logging
import pandas as pd
import numpy as np
from abc import ABC, abstractmethod
from typing import Dict, List, Optional, Any, Callable
from datetime import datetime, date
from dataclasses import dataclass, field
from enum import Enum
import json

logger = logging.getLogger(__name__)


class StrategyState(Enum):
    """策略状态枚举"""
    CREATED = "created"          # 已创建
    INITIALIZED = "initialized"  # 已初始化
    RUNNING = "running"         # 运行中
    PAUSED = "paused"          # 已暂停
    STOPPED = "stopped"        # 已停止
    ERROR = "error"            # 错误状态


@dataclass
class StrategyContext:
    """策略上下文"""
    strategy_id: str
    name: str
    version: str = "1.0.0"
    description: str = ""
    author: str = ""
    created_at: datetime = field(default_factory=datetime.now)
    updated_at: datetime = field(default_factory=datetime.now)

    # 策略配置
    parameters: Dict[str, Any] = field(default_factory=dict)
    universe: List[str] = field(default_factory=list)  # 股票池
    benchmark: str = "000300.SH"  # 基准指数

    # 运行时状态
    state: StrategyState = StrategyState.CREATED
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None

    # 性能指标
    total_return: float = 0.0
    annual_return: float = 0.0
    max_drawdown: float = 0.0
    sharpe_ratio: float = 0.0
    win_rate: float = 0.0

    # 统计信息
    total_trades: int = 0
    successful_trades: int = 0
    failed_trades: int = 0

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'strategy_id': self.strategy_id,
            'name': self.name,
            'version': self.version,
            'description': self.description,
            'author': self.author,
            'created_at': self.created_at.isoformat(),
            'updated_at': self.updated_at.isoformat(),
            'parameters': self.parameters,
            'universe': self.universe,
            'benchmark': self.benchmark,
            'state': self.state.value,
            'start_time': self.start_time.isoformat() if self.start_time else None,
            'end_time': self.end_time.isoformat() if self.end_time else None,
            'performance': {
                'total_return': self.total_return,
                'annual_return': self.annual_return,
                'max_drawdown': self.max_drawdown,
                'sharpe_ratio': self.sharpe_ratio,
                'win_rate': self.win_rate
            },
            'statistics': {
                'total_trades': self.total_trades,
                'successful_trades': self.successful_trades,
                'failed_trades': self.failed_trades
            }
        }


class BaseStrategy(ABC):
    """策略基类

    所有量化策略都应该继承此基类，并实现必要的抽象方法。
    提供策略的生命周期管理、参数配置、性能监控等基础功能。
    """

    def __init__(self, context: StrategyContext):
        self.context = context
        self._data_cache: Dict[str, pd.DataFrame] = {}
        self._indicators_cache: Dict[str, Any] = {}
        self._positions: Dict[str, float] = {}  # 持仓信息
        self._cash: float = 1000000.0  # 初始资金
        self._portfolio_value: float = self._cash
        self._trades_history: List[Dict[str, Any]] = []
        self._performance_history: List[Dict[str, Any]] = []

        # 回调函数
        self._on_data_callbacks: List[Callable] = []
        self._on_signal_callbacks: List[Callable] = []
        self._on_trade_callbacks: List[Callable] = []

        logger.info(f"策略初始化: {self.context.name} ({self.context.strategy_id})")

    @abstractmethod
    def initialize(self) -> bool:
        """初始化策略

        在策略开始运行前调用，用于设置初始参数、加载数据等。

        Returns:
            bool: 初始化是否成功
        """
        pass

    @abstractmethod
    def on_data(self, data: Dict[str, pd.DataFrame]) -> None:
        """数据更新回调

        当新的市场数据到达时调用此方法。

        Args:
            data: 市场数据，键为股票代码，值为DataFrame
        """
        pass

    @abstractmethod
    def generate_signals(self) -> List[Dict[str, Any]]:
        """生成交易信号

        根据当前市场数据和策略逻辑生成交易信号。

        Returns:
            List[Dict[str, Any]]: 交易信号列表
        """
        pass

    def start(self) -> bool:
        """启动策略"""
        try:
            if self.context.state != StrategyState.INITIALIZED:
                if not self.initialize():
                    logger.error("策略初始化失败")
                    return False
                self.context.state = StrategyState.INITIALIZED

            self.context.state = StrategyState.RUNNING
            self.context.start_time = datetime.now()

            logger.info(f"策略启动成功: {self.context.name}")
            return True

        except Exception as e:
            logger.error(f"策略启动失败: {e}")
            self.context.state = StrategyState.ERROR
            return False

    def stop(self) -> bool:
        """停止策略"""
        try:
            self.context.state = StrategyState.STOPPED
            self.context.end_time = datetime.now()

            logger.info(f"策略停止: {self.context.name}")
            return True

        except Exception as e:
            logger.error(f"策略停止失败: {e}")
            return False

    def get_parameter(self, key: str, default: Any = None) -> Any:
        """获取策略参数"""
        return self.context.parameters.get(key, default)

    def set_parameter(self, key: str, value: Any) -> None:
        """设置策略参数"""
        self.context.parameters[key] = value
        self.context.updated_at = datetime.now()

    def get_position(self, symbol: str) -> float:
        """获取持仓数量"""
        return self._positions.get(symbol, 0.0)

    def get_portfolio_value(self) -> float:
        """获取组合总价值"""
        return self._portfolio_value

    def get_performance_metrics(self) -> Dict[str, float]:
        """获取性能指标"""
        return {
            'total_return': self.context.total_return,
            'annual_return': self.context.annual_return,
            'max_drawdown': self.context.max_drawdown,
            'sharpe_ratio': self.context.sharpe_ratio,
            'win_rate': self.context.win_rate
        }

    def __str__(self) -> str:
        return f"Strategy({self.context.name}, {self.context.state.value})"