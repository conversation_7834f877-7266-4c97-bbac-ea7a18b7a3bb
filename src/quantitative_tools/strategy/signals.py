"""
交易信号生成和管理

提供交易信号的生成、过滤、验证和管理功能，包括：
- 信号生成器基类
- 常用技术指标信号
- 信号强度计算
- 信号过滤和验证
"""

import logging
import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Any
from datetime import datetime
from dataclasses import dataclass
from enum import Enum

logger = logging.getLogger(__name__)


class SignalType(Enum):
    """信号类型"""
    BUY = "buy"
    SELL = "sell"
    HOLD = "hold"


class SignalStrength(Enum):
    """信号强度"""
    WEAK = "weak"
    MEDIUM = "medium"
    STRONG = "strong"


@dataclass
class TradingSignal:
    """交易信号"""
    symbol: str
    signal_type: SignalType
    strength: SignalStrength
    price: float
    quantity: int
    timestamp: datetime
    strategy_id: str
    confidence: float = 0.0
    metadata: Dict[str, Any] = None

    def __post_init__(self):
        if self.metadata is None:
            self.metadata = {}

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'symbol': self.symbol,
            'signal_type': self.signal_type.value,
            'strength': self.strength.value,
            'price': self.price,
            'quantity': self.quantity,
            'timestamp': self.timestamp.isoformat(),
            'strategy_id': self.strategy_id,
            'confidence': self.confidence,
            'metadata': self.metadata
        }


class SignalGenerator:
    """信号生成器"""

    def __init__(self, strategy_id: str):
        self.strategy_id = strategy_id
        self._signals_history: List[TradingSignal] = []

    def generate_ma_crossover_signal(self, data: pd.DataFrame,
                                   short_window: int = 5,
                                   long_window: int = 20) -> List[TradingSignal]:
        """生成移动平均交叉信号"""
        signals = []

        if len(data) < long_window:
            return signals

        # 计算移动平均线
        data['ma_short'] = data['close'].rolling(window=short_window).mean()
        data['ma_long'] = data['close'].rolling(window=long_window).mean()

        # 生成信号
        for i in range(1, len(data)):
            current_short = data['ma_short'].iloc[i]
            current_long = data['ma_long'].iloc[i]
            prev_short = data['ma_short'].iloc[i-1]
            prev_long = data['ma_long'].iloc[i-1]

            if pd.isna(current_short) or pd.isna(current_long):
                continue

            # 金叉：短期均线上穿长期均线
            if prev_short <= prev_long and current_short > current_long:
                signal = TradingSignal(
                    symbol=data['symbol'].iloc[i] if 'symbol' in data.columns else 'UNKNOWN',
                    signal_type=SignalType.BUY,
                    strength=SignalStrength.MEDIUM,
                    price=data['close'].iloc[i],
                    quantity=100,
                    timestamp=datetime.now(),
                    strategy_id=self.strategy_id,
                    confidence=0.7,
                    metadata={'indicator': 'ma_crossover', 'short_ma': current_short, 'long_ma': current_long}
                )
                signals.append(signal)

            # 死叉：短期均线下穿长期均线
            elif prev_short >= prev_long and current_short < current_long:
                signal = TradingSignal(
                    symbol=data['symbol'].iloc[i] if 'symbol' in data.columns else 'UNKNOWN',
                    signal_type=SignalType.SELL,
                    strength=SignalStrength.MEDIUM,
                    price=data['close'].iloc[i],
                    quantity=100,
                    timestamp=datetime.now(),
                    strategy_id=self.strategy_id,
                    confidence=0.7,
                    metadata={'indicator': 'ma_crossover', 'short_ma': current_short, 'long_ma': current_long}
                )
                signals.append(signal)

        self._signals_history.extend(signals)
        return signals

    def generate_rsi_signal(self, data: pd.DataFrame,
                           period: int = 14,
                           oversold: float = 30,
                           overbought: float = 70) -> List[TradingSignal]:
        """生成RSI信号"""
        signals = []

        if len(data) < period + 1:
            return signals

        # 计算RSI
        delta = data['close'].diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
        rs = gain / loss
        rsi = 100 - (100 / (1 + rs))

        # 生成信号
        for i in range(period, len(data)):
            current_rsi = rsi.iloc[i]

            if pd.isna(current_rsi):
                continue

            # 超卖信号
            if current_rsi < oversold:
                signal = TradingSignal(
                    symbol=data['symbol'].iloc[i] if 'symbol' in data.columns else 'UNKNOWN',
                    signal_type=SignalType.BUY,
                    strength=SignalStrength.STRONG if current_rsi < 20 else SignalStrength.MEDIUM,
                    price=data['close'].iloc[i],
                    quantity=100,
                    timestamp=datetime.now(),
                    strategy_id=self.strategy_id,
                    confidence=0.8 if current_rsi < 20 else 0.6,
                    metadata={'indicator': 'rsi', 'rsi_value': current_rsi}
                )
                signals.append(signal)

            # 超买信号
            elif current_rsi > overbought:
                signal = TradingSignal(
                    symbol=data['symbol'].iloc[i] if 'symbol' in data.columns else 'UNKNOWN',
                    signal_type=SignalType.SELL,
                    strength=SignalStrength.STRONG if current_rsi > 80 else SignalStrength.MEDIUM,
                    price=data['close'].iloc[i],
                    quantity=100,
                    timestamp=datetime.now(),
                    strategy_id=self.strategy_id,
                    confidence=0.8 if current_rsi > 80 else 0.6,
                    metadata={'indicator': 'rsi', 'rsi_value': current_rsi}
                )
                signals.append(signal)

        self._signals_history.extend(signals)
        return signals

    def filter_signals(self, signals: List[TradingSignal],
                      min_confidence: float = 0.5) -> List[TradingSignal]:
        """过滤信号"""
        return [signal for signal in signals if signal.confidence >= min_confidence]

    def get_signals_history(self) -> List[TradingSignal]:
        """获取信号历史"""
        return self._signals_history.copy()

    def clear_history(self):
        """清空信号历史"""
        self._signals_history.clear()


def create_simple_strategy_signals(data: pd.DataFrame, strategy_id: str) -> List[TradingSignal]:
    """创建简单策略信号的便捷函数"""
    generator = SignalGenerator(strategy_id)

    # 生成移动平均交叉信号
    ma_signals = generator.generate_ma_crossover_signal(data)

    # 生成RSI信号
    rsi_signals = generator.generate_rsi_signal(data)

    # 合并和过滤信号
    all_signals = ma_signals + rsi_signals
    filtered_signals = generator.filter_signals(all_signals, min_confidence=0.6)

    return filtered_signals