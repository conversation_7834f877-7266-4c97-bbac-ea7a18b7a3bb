"""
风控规则引擎

提供灵活的风险控制规则系统，包括：
- 规则定义和管理
- 实时风险检查
- 风险限额管理
- 违规处理机制
- 风控报告生成
"""

import logging
from typing import Dict, List, Optional, Any, Callable
from datetime import datetime, timedelta
from dataclasses import dataclass
from enum import Enum

logger = logging.getLogger(__name__)


class RiskLevel(Enum):
    """风险级别"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


class RuleAction(Enum):
    """规则动作"""
    WARN = "warn"           # 警告
    BLOCK = "block"         # 阻止
    REDUCE = "reduce"       # 减少
    LIQUIDATE = "liquidate" # 清仓


@dataclass
class RiskRule:
    """风险规则"""
    rule_id: str
    name: str
    description: str
    check_function: Callable
    risk_level: RiskLevel
    action: RuleAction
    threshold: float
    enabled: bool = True
    created_at: datetime = None

    def __post_init__(self):
        if self.created_at is None:
            self.created_at = datetime.now()


@dataclass
class RiskViolation:
    """风险违规记录"""
    rule_id: str
    rule_name: str
    risk_level: RiskLevel
    action: RuleAction
    current_value: float
    threshold: float
    message: str
    timestamp: datetime = None
    context: Dict[str, Any] = None

    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.now()
        if self.context is None:
            self.context = {}


class RiskRuleEngine:
    """风控规则引擎"""

    def __init__(self):
        self._rules: Dict[str, RiskRule] = {}
        self._violations: List[RiskViolation] = []
        self._callbacks: List[Callable] = []

        # 初始化默认规则
        self._init_default_rules()

    def _init_default_rules(self):
        """初始化默认风控规则"""
        # 单股持仓比例限制
        self.add_rule(
            rule_id="single_position_limit",
            name="单股持仓比例限制",
            description="单只股票持仓不超过总资产的20%",
            check_function=self._check_single_position_limit,
            risk_level=RiskLevel.HIGH,
            action=RuleAction.BLOCK,
            threshold=0.20
        )

        # 总仓位限制
        self.add_rule(
            rule_id="total_position_limit",
            name="总仓位限制",
            description="总仓位不超过总资产的95%",
            check_function=self._check_total_position_limit,
            risk_level=RiskLevel.MEDIUM,
            action=RuleAction.WARN,
            threshold=0.95
        )

        # 日内交易次数限制
        self.add_rule(
            rule_id="daily_trade_limit",
            name="日内交易次数限制",
            description="单日交易次数不超过100次",
            check_function=self._check_daily_trade_limit,
            risk_level=RiskLevel.MEDIUM,
            action=RuleAction.BLOCK,
            threshold=100
        )

        # 最大回撤限制
        self.add_rule(
            rule_id="max_drawdown_limit",
            name="最大回撤限制",
            description="最大回撤不超过10%",
            check_function=self._check_max_drawdown_limit,
            risk_level=RiskLevel.CRITICAL,
            action=RuleAction.LIQUIDATE,
            threshold=0.10
        )

    def add_rule(self, rule_id: str, name: str, description: str,
                check_function: Callable, risk_level: RiskLevel,
                action: RuleAction, threshold: float) -> bool:
        """添加风控规则"""
        try:
            rule = RiskRule(
                rule_id=rule_id,
                name=name,
                description=description,
                check_function=check_function,
                risk_level=risk_level,
                action=action,
                threshold=threshold
            )

            self._rules[rule_id] = rule
            logger.info(f"添加风控规则: {name}")
            return True

        except Exception as e:
            logger.error(f"添加风控规则失败: {e}")
            return False

    def remove_rule(self, rule_id: str) -> bool:
        """移除风控规则"""
        if rule_id in self._rules:
            del self._rules[rule_id]
            logger.info(f"移除风控规则: {rule_id}")
            return True
        return False

    def enable_rule(self, rule_id: str) -> bool:
        """启用规则"""
        if rule_id in self._rules:
            self._rules[rule_id].enabled = True
            return True
        return False

    def disable_rule(self, rule_id: str) -> bool:
        """禁用规则"""
        if rule_id in self._rules:
            self._rules[rule_id].enabled = False
            return True
        return False

    def check_all_rules(self, context: Dict[str, Any]) -> List[RiskViolation]:
        """检查所有风控规则"""
        violations = []

        for rule in self._rules.values():
            if not rule.enabled:
                continue

            try:
                violation = self._check_rule(rule, context)
                if violation:
                    violations.append(violation)
                    self._violations.append(violation)

                    # 通知回调
                    self._notify_callbacks(violation)

            except Exception as e:
                logger.error(f"规则检查失败 {rule.rule_id}: {e}")

        return violations

    def _check_rule(self, rule: RiskRule, context: Dict[str, Any]) -> Optional[RiskViolation]:
        """检查单个规则"""
        try:
            result = rule.check_function(context, rule.threshold)

            if isinstance(result, dict):
                violated = result.get('violated', False)
                current_value = result.get('current_value', 0)
                message = result.get('message', f"违反规则: {rule.name}")
            else:
                violated = result
                current_value = 0
                message = f"违反规则: {rule.name}"

            if violated:
                return RiskViolation(
                    rule_id=rule.rule_id,
                    rule_name=rule.name,
                    risk_level=rule.risk_level,
                    action=rule.action,
                    current_value=current_value,
                    threshold=rule.threshold,
                    message=message,
                    context=context
                )

            return None

        except Exception as e:
            logger.error(f"规则检查异常 {rule.rule_id}: {e}")
            return None

    def _check_single_position_limit(self, context: Dict[str, Any], threshold: float) -> Dict[str, Any]:
        """检查单股持仓比例"""
        portfolio_value = context.get('portfolio_value', 0)
        positions = context.get('positions', {})

        if portfolio_value == 0:
            return {'violated': False}

        for symbol, position in positions.items():
            position_value = abs(position.get('market_value', 0))
            position_ratio = position_value / portfolio_value

            if position_ratio > threshold:
                return {
                    'violated': True,
                    'current_value': position_ratio,
                    'message': f"股票 {symbol} 持仓比例 {position_ratio:.2%} 超过限制 {threshold:.2%}"
                }

        return {'violated': False}

    def _check_total_position_limit(self, context: Dict[str, Any], threshold: float) -> Dict[str, Any]:
        """检查总仓位限制"""
        portfolio_value = context.get('portfolio_value', 0)
        cash = context.get('cash', 0)

        if portfolio_value == 0:
            return {'violated': False}

        position_value = portfolio_value - cash
        position_ratio = position_value / portfolio_value

        if position_ratio > threshold:
            return {
                'violated': True,
                'current_value': position_ratio,
                'message': f"总仓位比例 {position_ratio:.2%} 超过限制 {threshold:.2%}"
            }

        return {'violated': False}

    def _check_daily_trade_limit(self, context: Dict[str, Any], threshold: float) -> Dict[str, Any]:
        """检查日内交易次数"""
        daily_trades = context.get('daily_trades', 0)

        if daily_trades > threshold:
            return {
                'violated': True,
                'current_value': daily_trades,
                'message': f"日内交易次数 {daily_trades} 超过限制 {threshold}"
            }

        return {'violated': False}

    def _check_max_drawdown_limit(self, context: Dict[str, Any], threshold: float) -> Dict[str, Any]:
        """检查最大回撤限制"""
        max_drawdown = context.get('max_drawdown', 0)

        if max_drawdown > threshold:
            return {
                'violated': True,
                'current_value': max_drawdown,
                'message': f"最大回撤 {max_drawdown:.2%} 超过限制 {threshold:.2%}"
            }

        return {'violated': False}

    def add_callback(self, callback: Callable):
        """添加违规回调"""
        self._callbacks.append(callback)

    def _notify_callbacks(self, violation: RiskViolation):
        """通知回调函数"""
        for callback in self._callbacks:
            try:
                callback(violation)
            except Exception as e:
                logger.error(f"风控回调执行失败: {e}")

    def get_violations(self, hours: int = 24) -> List[RiskViolation]:
        """获取指定时间内的违规记录"""
        cutoff_time = datetime.now() - timedelta(hours=hours)
        return [v for v in self._violations if v.timestamp >= cutoff_time]

    def get_rules(self) -> List[RiskRule]:
        """获取所有规则"""
        return list(self._rules.values())

    def get_rule_stats(self) -> Dict[str, Any]:
        """获取规则统计信息"""
        total_rules = len(self._rules)
        enabled_rules = len([r for r in self._rules.values() if r.enabled])
        total_violations = len(self._violations)

        # 按风险级别统计违规
        violation_by_level = {}
        for level in RiskLevel:
            violation_by_level[level.value] = len([v for v in self._violations if v.risk_level == level])

        return {
            'total_rules': total_rules,
            'enabled_rules': enabled_rules,
            'disabled_rules': total_rules - enabled_rules,
            'total_violations': total_violations,
            'violations_by_level': violation_by_level
        }


# 全局风控规则引擎实例
risk_rule_engine = RiskRuleEngine()