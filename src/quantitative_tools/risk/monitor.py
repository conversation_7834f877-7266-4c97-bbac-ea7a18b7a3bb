"""
风险监控系统

提供实时风险监控和预警功能，包括：
- 实时风险指标监控
- 风险预警和通知
- 风险报告生成
- 历史风险分析
- 风险仪表板数据
"""

import logging
import asyncio
from typing import Dict, List, Optional, Any, Callable
from datetime import datetime, timedelta
import pandas as pd

from .calculator import RiskCalculator, risk_calculator
from .rules import RiskRuleEngine, RiskViolation, risk_rule_engine

logger = logging.getLogger(__name__)


class RiskMonitor:
    """风险监控器"""

    def __init__(self, risk_calc: RiskCalculator = None,
                 rule_engine: RiskRuleEngine = None):
        self.risk_calculator = risk_calc or risk_calculator
        self.rule_engine = rule_engine or risk_rule_engine

        # 监控配置
        self.monitoring_interval = 60  # 监控间隔（秒）
        self.alert_thresholds = {
            'var_95': 0.05,      # VaR 95% 阈值
            'max_drawdown': 0.10, # 最大回撤阈值
            'volatility': 0.30    # 波动率阈值
        }

        # 监控状态
        self._monitoring = False
        self._monitor_task = None

        # 风险数据历史
        self._risk_history: List[Dict[str, Any]] = []
        self._alert_history: List[Dict[str, Any]] = []

        # 回调函数
        self._alert_callbacks: List[Callable] = []

    def start_monitoring(self, portfolio_data_source: Callable):
        """启动风险监控"""
        if self._monitoring:
            logger.warning("风险监控已在运行")
            return

        self._monitoring = True
        self._portfolio_data_source = portfolio_data_source

        # 启动监控任务
        self._monitor_task = asyncio.create_task(self._monitor_loop())

        logger.info("风险监控启动")

    def stop_monitoring(self):
        """停止风险监控"""
        self._monitoring = False

        if self._monitor_task:
            self._monitor_task.cancel()

        logger.info("风险监控停止")

    async def _monitor_loop(self):
        """监控循环"""
        while self._monitoring:
            try:
                # 获取组合数据
                portfolio_data = self._portfolio_data_source()

                # 计算风险指标
                risk_metrics = self._calculate_current_risk(portfolio_data)

                # 检查风控规则
                violations = self._check_risk_rules(portfolio_data)

                # 检查预警条件
                alerts = self._check_alerts(risk_metrics)

                # 记录风险数据
                self._record_risk_data(risk_metrics, violations, alerts)

                # 处理预警
                if alerts:
                    self._handle_alerts(alerts)

                # 处理违规
                if violations:
                    self._handle_violations(violations)

                await asyncio.sleep(self.monitoring_interval)

            except Exception as e:
                logger.error(f"风险监控异常: {e}")
                await asyncio.sleep(self.monitoring_interval)

    def _calculate_current_risk(self, portfolio_data: Dict[str, Any]) -> Dict[str, Any]:
        """计算当前风险指标"""
        try:
            returns = portfolio_data.get('returns', pd.Series())
            prices = portfolio_data.get('prices', pd.Series())

            # 计算风险指标
            risk_metrics = self.risk_calculator.calculate_risk_metrics(returns, prices)

            # 添加组合相关指标
            risk_metrics['portfolio_value'] = portfolio_data.get('portfolio_value', 0)
            risk_metrics['cash_ratio'] = portfolio_data.get('cash', 0) / risk_metrics['portfolio_value'] if risk_metrics['portfolio_value'] > 0 else 0
            risk_metrics['positions_count'] = len(portfolio_data.get('positions', {}))

            return risk_metrics

        except Exception as e:
            logger.error(f"风险指标计算失败: {e}")
            return {}

    def _check_risk_rules(self, portfolio_data: Dict[str, Any]) -> List[RiskViolation]:
        """检查风控规则"""
        try:
            context = {
                'portfolio_value': portfolio_data.get('portfolio_value', 0),
                'cash': portfolio_data.get('cash', 0),
                'positions': portfolio_data.get('positions', {}),
                'daily_trades': portfolio_data.get('daily_trades', 0),
                'max_drawdown': portfolio_data.get('max_drawdown', 0)
            }

            violations = self.rule_engine.check_all_rules(context)
            return violations

        except Exception as e:
            logger.error(f"风控规则检查失败: {e}")
            return []

    def _check_alerts(self, risk_metrics: Dict[str, Any]) -> List[Dict[str, Any]]:
        """检查预警条件"""
        alerts = []

        try:
            # VaR预警
            var_95 = risk_metrics.get('var_95', 0)
            if var_95 > self.alert_thresholds['var_95']:
                alerts.append({
                    'type': 'var_alert',
                    'level': 'warning',
                    'message': f"VaR 95% 超过阈值: {var_95:.2%} > {self.alert_thresholds['var_95']:.2%}",
                    'value': var_95,
                    'threshold': self.alert_thresholds['var_95']
                })

            # 最大回撤预警
            max_drawdown = risk_metrics.get('max_drawdown', 0)
            if max_drawdown > self.alert_thresholds['max_drawdown']:
                alerts.append({
                    'type': 'drawdown_alert',
                    'level': 'critical',
                    'message': f"最大回撤超过阈值: {max_drawdown:.2%} > {self.alert_thresholds['max_drawdown']:.2%}",
                    'value': max_drawdown,
                    'threshold': self.alert_thresholds['max_drawdown']
                })

            # 波动率预警
            volatility = risk_metrics.get('volatility', 0)
            if volatility > self.alert_thresholds['volatility']:
                alerts.append({
                    'type': 'volatility_alert',
                    'level': 'warning',
                    'message': f"波动率超过阈值: {volatility:.2%} > {self.alert_thresholds['volatility']:.2%}",
                    'value': volatility,
                    'threshold': self.alert_thresholds['volatility']
                })

        except Exception as e:
            logger.error(f"预警检查失败: {e}")

        return alerts

    def _record_risk_data(self, risk_metrics: Dict[str, Any],
                         violations: List[RiskViolation],
                         alerts: List[Dict[str, Any]]):
        """记录风险数据"""
        record = {
            'timestamp': datetime.now(),
            'risk_metrics': risk_metrics,
            'violations_count': len(violations),
            'alerts_count': len(alerts)
        }

        self._risk_history.append(record)

        # 保留最近24小时的数据
        cutoff_time = datetime.now() - timedelta(hours=24)
        self._risk_history = [r for r in self._risk_history if r['timestamp'] >= cutoff_time]

    def _handle_alerts(self, alerts: List[Dict[str, Any]]):
        """处理预警"""
        for alert in alerts:
            alert['timestamp'] = datetime.now()
            self._alert_history.append(alert)

            logger.warning(f"风险预警: {alert['message']}")

            # 通知回调
            for callback in self._alert_callbacks:
                try:
                    callback(alert)
                except Exception as e:
                    logger.error(f"预警回调执行失败: {e}")

    def _handle_violations(self, violations: List[RiskViolation]):
        """处理违规"""
        for violation in violations:
            logger.error(f"风控违规: {violation.message}")

    def add_alert_callback(self, callback: Callable):
        """添加预警回调"""
        self._alert_callbacks.append(callback)

    def get_current_risk_status(self) -> Dict[str, Any]:
        """获取当前风险状态"""
        if not self._risk_history:
            return {}

        latest_record = self._risk_history[-1]
        recent_violations = self.rule_engine.get_violations(hours=1)
        recent_alerts = [a for a in self._alert_history
                        if a['timestamp'] >= datetime.now() - timedelta(hours=1)]

        return {
            'timestamp': latest_record['timestamp'],
            'risk_metrics': latest_record['risk_metrics'],
            'recent_violations': len(recent_violations),
            'recent_alerts': len(recent_alerts),
            'monitoring_status': 'active' if self._monitoring else 'inactive'
        }

    def get_risk_report(self, hours: int = 24) -> Dict[str, Any]:
        """生成风险报告"""
        cutoff_time = datetime.now() - timedelta(hours=hours)

        # 筛选时间范围内的数据
        recent_history = [r for r in self._risk_history if r['timestamp'] >= cutoff_time]
        recent_alerts = [a for a in self._alert_history if a['timestamp'] >= cutoff_time]
        recent_violations = self.rule_engine.get_violations(hours=hours)

        if not recent_history:
            return {'error': '没有风险数据'}

        # 计算统计信息
        latest_metrics = recent_history[-1]['risk_metrics']

        # 风险趋势分析
        if len(recent_history) > 1:
            first_metrics = recent_history[0]['risk_metrics']
            var_trend = latest_metrics.get('var_95', 0) - first_metrics.get('var_95', 0)
            volatility_trend = latest_metrics.get('volatility', 0) - first_metrics.get('volatility', 0)
        else:
            var_trend = 0
            volatility_trend = 0

        report = {
            'report_period': f"{hours}小时",
            'generated_at': datetime.now(),
            'current_metrics': latest_metrics,
            'trends': {
                'var_95_change': var_trend,
                'volatility_change': volatility_trend
            },
            'alerts_summary': {
                'total_alerts': len(recent_alerts),
                'by_type': {}
            },
            'violations_summary': {
                'total_violations': len(recent_violations),
                'by_level': {}
            },
            'recommendations': self._generate_recommendations(latest_metrics, recent_alerts, recent_violations)
        }

        # 按类型统计预警
        for alert in recent_alerts:
            alert_type = alert.get('type', 'unknown')
            report['alerts_summary']['by_type'][alert_type] = report['alerts_summary']['by_type'].get(alert_type, 0) + 1

        # 按级别统计违规
        for violation in recent_violations:
            level = violation.risk_level.value
            report['violations_summary']['by_level'][level] = report['violations_summary']['by_level'].get(level, 0) + 1

        return report

    def _generate_recommendations(self, metrics: Dict[str, Any],
                                alerts: List[Dict[str, Any]],
                                violations: List[RiskViolation]) -> List[str]:
        """生成风险建议"""
        recommendations = []

        # 基于风险指标的建议
        if metrics.get('max_drawdown', 0) > 0.05:
            recommendations.append("当前回撤较大，建议降低仓位或调整策略")

        if metrics.get('volatility', 0) > 0.25:
            recommendations.append("组合波动率较高，建议增加防御性资产配置")

        if metrics.get('var_95', 0) > 0.03:
            recommendations.append("VaR风险较高，建议进行风险对冲")

        # 基于预警的建议
        if any(a['type'] == 'drawdown_alert' for a in alerts):
            recommendations.append("触发回撤预警，建议立即检查策略表现")

        # 基于违规的建议
        if any(v.action.value == 'liquidate' for v in violations):
            recommendations.append("触发清仓规则，建议立即减仓")

        if not recommendations:
            recommendations.append("当前风险水平正常，继续监控")

        return recommendations


# 全局风险监控器实例
risk_monitor = RiskMonitor()