"""
风险计算引擎

提供全面的风险指标计算功能，包括：
- VaR (Value at Risk) 计算
- 最大回撤计算
- 波动率分析
- 相关性分析
- 风险敞口计算
"""

import logging
import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta

logger = logging.getLogger(__name__)


class RiskCalculator:
    """风险计算器"""

    def __init__(self):
        self.confidence_levels = [0.95, 0.99]  # VaR置信水平

    def calculate_var(self, returns: pd.Series, confidence_level: float = 0.95) -> float:
        """计算VaR (Value at Risk) - 历史模拟法"""
        try:
            if len(returns) == 0:
                return 0.0

            # 计算分位数
            alpha = 1 - confidence_level
            var = np.percentile(returns, alpha * 100)
            return abs(var)

        except Exception as e:
            logger.error(f"VaR计算失败: {e}")
            return 0.0

    def calculate_max_drawdown(self, prices: pd.Series) -> Dict[str, Any]:
        """计算最大回撤"""
        try:
            if len(prices) == 0:
                return {'max_drawdown': 0.0, 'max_drawdown_pct': 0.0}

            # 计算累计最高点
            cumulative_max = prices.expanding().max()

            # 计算回撤
            drawdown = (prices - cumulative_max) / cumulative_max

            # 找到最大回撤
            max_drawdown = drawdown.min()

            return {
                'max_drawdown': abs(max_drawdown),
                'max_drawdown_pct': abs(max_drawdown) * 100
            }

        except Exception as e:
            logger.error(f"最大回撤计算失败: {e}")
            return {'max_drawdown': 0.0, 'max_drawdown_pct': 0.0}

    def calculate_volatility(self, returns: pd.Series, annualize: bool = True) -> float:
        """计算波动率"""
        try:
            if len(returns) == 0:
                return 0.0

            volatility = returns.std()

            if annualize:
                # 年化波动率（假设252个交易日）
                volatility *= np.sqrt(252)

            return volatility

        except Exception as e:
            logger.error(f"波动率计算失败: {e}")
            return 0.0

    def calculate_sharpe_ratio(self, returns: pd.Series, risk_free_rate: float = 0.03) -> float:
        """计算夏普比率"""
        try:
            if len(returns) == 0:
                return 0.0

            excess_returns = returns.mean() * 252 - risk_free_rate  # 年化超额收益
            volatility = self.calculate_volatility(returns, annualize=True)

            if volatility == 0:
                return 0.0

            sharpe_ratio = excess_returns / volatility
            return sharpe_ratio

        except Exception as e:
            logger.error(f"夏普比率计算失败: {e}")
            return 0.0

    def calculate_risk_metrics(self, returns: pd.Series, prices: pd.Series = None) -> Dict[str, Any]:
        """计算综合风险指标"""
        try:
            metrics = {}

            # 基础统计
            metrics['mean_return'] = returns.mean() * 252 if len(returns) > 0 else 0  # 年化收益率
            metrics['volatility'] = self.calculate_volatility(returns)

            # VaR指标
            for confidence in self.confidence_levels:
                metrics[f'var_{int(confidence*100)}'] = self.calculate_var(returns, confidence)

            # 最大回撤
            if prices is not None and len(prices) > 0:
                drawdown_info = self.calculate_max_drawdown(prices)
                metrics.update(drawdown_info)

            # 夏普比率
            metrics['sharpe_ratio'] = self.calculate_sharpe_ratio(returns)

            return metrics

        except Exception as e:
            logger.error(f"风险指标计算失败: {e}")
            return {}


# 全局风险计算器实例
risk_calculator = RiskCalculator()