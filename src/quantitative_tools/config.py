"""
系统配置管理模块

提供统一的配置管理功能，支持环境变量、配置文件等多种配置方式。
所有配置项都有详细的中文注释说明。
"""

import os
from typing import Any, Dict, List, Optional

try:
    from pydantic_settings import BaseSettings
    from pydantic import Field, validator
except ImportError:
    try:
        from pydantic import BaseSettings, Field, validator
    except ImportError:
        # 如果Pydantic未安装，创建简单的配置类
        class BaseSettings:
            def __init__(self, **kwargs):
                for key, value in kwargs.items():
                    setattr(self, key, value)

        def Field(default=None, description=""):
            return default

        def validator(*args, **kwargs):
            def decorator(func):
                return func
            return decorator


class DatabaseSettings(BaseSettings):
    """数据库配置"""
    
    # PostgreSQL主数据库配置
    postgres_host: str = Field(default="localhost", description="PostgreSQL主机地址")
    postgres_port: int = Field(default=5432, description="PostgreSQL端口")
    postgres_user: str = Field(default="postgres", description="PostgreSQL用户名")
    postgres_password: str = Field(default="password", description="PostgreSQL密码")
    postgres_db: str = Field(default="quantitative_tools", description="PostgreSQL数据库名")
    
    # ClickHouse时序数据库配置
    clickhouse_host: str = Field(default="localhost", description="ClickHouse主机地址")
    clickhouse_port: int = Field(default=9000, description="ClickHouse端口")
    clickhouse_user: str = Field(default="default", description="ClickHouse用户名")
    clickhouse_password: str = Field(default="", description="ClickHouse密码")
    clickhouse_db: str = Field(default="market_data", description="ClickHouse数据库名")
    
    # Redis缓存配置
    redis_host: str = Field(default="localhost", description="Redis主机地址")
    redis_port: int = Field(default=6379, description="Redis端口")
    redis_password: Optional[str] = Field(default=None, description="Redis密码")
    redis_db: int = Field(default=0, description="Redis数据库编号")
    
    @property
    def postgres_url(self) -> str:
        """获取PostgreSQL连接URL"""
        return (
            f"postgresql://{self.postgres_user}:{self.postgres_password}"
            f"@{self.postgres_host}:{self.postgres_port}/{self.postgres_db}"
        )
    
    @property
    def clickhouse_url(self) -> str:
        """获取ClickHouse连接URL"""
        return f"clickhouse://{self.clickhouse_host}:{self.clickhouse_port}/{self.clickhouse_db}"
    
    @property
    def redis_url(self) -> str:
        """获取Redis连接URL"""
        auth = f":{self.redis_password}@" if self.redis_password else ""
        return f"redis://{auth}{self.redis_host}:{self.redis_port}/{self.redis_db}"


class SecuritySettings(BaseSettings):
    """安全配置"""
    
    # JWT令牌配置
    secret_key: str = Field(
        default="your-secret-key-change-in-production",
        description="JWT密钥，生产环境必须修改"
    )
    algorithm: str = Field(default="HS256", description="JWT加密算法")
    access_token_expire_minutes: int = Field(default=15, description="访问令牌过期时间（分钟）")
    refresh_token_expire_days: int = Field(default=7, description="刷新令牌过期时间（天）")
    
    # 密码配置
    password_min_length: int = Field(default=8, description="密码最小长度")
    password_require_uppercase: bool = Field(default=True, description="密码是否需要大写字母")
    password_require_lowercase: bool = Field(default=True, description="密码是否需要小写字母")
    password_require_numbers: bool = Field(default=True, description="密码是否需要数字")
    password_require_special: bool = Field(default=True, description="密码是否需要特殊字符")
    
    # API限流配置
    rate_limit_per_minute: int = Field(default=1000, description="每分钟API请求限制")
    rate_limit_per_hour: int = Field(default=10000, description="每小时API请求限制")
    
    # 会话配置
    session_timeout_minutes: int = Field(default=30, description="会话超时时间（分钟）")
    max_login_attempts: int = Field(default=5, description="最大登录尝试次数")
    lockout_duration_minutes: int = Field(default=15, description="账户锁定时间（分钟）")
    
    @validator("secret_key")
    def validate_secret_key(cls, v: str) -> str:
        """验证密钥强度"""
        if len(v) < 32:
            raise ValueError("密钥长度至少需要32个字符")
        return v


class DataSourceSettings(BaseSettings):
    """数据源配置"""
    
    # Wind数据源配置
    wind_enabled: bool = Field(default=False, description="是否启用Wind数据源")
    wind_username: Optional[str] = Field(default=None, description="Wind用户名")
    wind_password: Optional[str] = Field(default=None, description="Wind密码")
    
    # 东方财富数据源配置
    eastmoney_enabled: bool = Field(default=True, description="是否启用东方财富数据源")
    eastmoney_token: Optional[str] = Field(default=None, description="东方财富API令牌")
    
    # Tushare数据源配置
    tushare_enabled: bool = Field(default=True, description="是否启用Tushare数据源")
    tushare_token: Optional[str] = Field(default=None, description="Tushare API令牌")
    
    # 数据更新配置
    realtime_data_interval: int = Field(default=1, description="实时数据更新间隔（秒）")
    daily_data_update_time: str = Field(default="18:00", description="日线数据更新时间")
    data_retention_days: int = Field(default=365, description="数据保留天数")
    
    # 数据质量配置
    data_quality_check_enabled: bool = Field(default=True, description="是否启用数据质量检查")
    max_missing_data_ratio: float = Field(default=0.05, description="最大数据缺失比例")
    outlier_detection_enabled: bool = Field(default=True, description="是否启用异常值检测")


class TradingSettings(BaseSettings):
    """交易配置"""
    
    # 交易时间配置
    market_open_time: str = Field(default="09:30", description="市场开盘时间")
    market_close_time: str = Field(default="15:00", description="市场收盘时间")
    trading_days_only: bool = Field(default=True, description="是否仅在交易日执行")
    
    # 订单配置
    default_order_timeout: int = Field(default=300, description="默认订单超时时间（秒）")
    max_order_size: int = Field(default=1000000, description="最大单笔订单数量")
    min_order_size: int = Field(default=100, description="最小单笔订单数量")
    
    # 风控配置
    max_position_ratio: float = Field(default=0.1, description="单股最大持仓比例")
    max_daily_loss_ratio: float = Field(default=0.05, description="单日最大亏损比例")
    stop_loss_ratio: float = Field(default=0.1, description="默认止损比例")
    
    # 交易成本配置
    commission_rate: float = Field(default=0.0003, description="手续费率")
    stamp_tax_rate: float = Field(default=0.001, description="印花税率")
    transfer_fee_rate: float = Field(default=0.00002, description="过户费率")
    slippage_rate: float = Field(default=0.001, description="滑点率")


class MonitoringSettings(BaseSettings):
    """监控配置"""
    
    # 日志配置
    log_level: str = Field(default="INFO", description="日志级别")
    log_format: str = Field(default="json", description="日志格式")
    log_file_path: str = Field(default="logs/app.log", description="日志文件路径")
    log_max_size: str = Field(default="100MB", description="日志文件最大大小")
    log_backup_count: int = Field(default=10, description="日志文件备份数量")
    
    # 监控配置
    metrics_enabled: bool = Field(default=True, description="是否启用指标监控")
    metrics_port: int = Field(default=8080, description="指标监控端口")
    health_check_interval: int = Field(default=30, description="健康检查间隔（秒）")
    
    # 告警配置
    alert_enabled: bool = Field(default=True, description="是否启用告警")
    alert_email_enabled: bool = Field(default=True, description="是否启用邮件告警")
    alert_sms_enabled: bool = Field(default=False, description="是否启用短信告警")
    alert_webhook_enabled: bool = Field(default=False, description="是否启用Webhook告警")
    
    # 邮件配置
    smtp_host: Optional[str] = Field(default=None, description="SMTP服务器地址")
    smtp_port: int = Field(default=587, description="SMTP服务器端口")
    smtp_username: Optional[str] = Field(default=None, description="SMTP用户名")
    smtp_password: Optional[str] = Field(default=None, description="SMTP密码")
    smtp_use_tls: bool = Field(default=True, description="是否使用TLS加密")


class ApplicationSettings(BaseSettings):
    """应用配置"""
    
    # 基础配置
    app_name: str = Field(default="AI量化交易工具", description="应用名称")
    app_version: str = Field(default="1.0.0", description="应用版本")
    debug: bool = Field(default=False, description="是否开启调试模式")
    environment: str = Field(default="development", description="运行环境")
    
    # 服务器配置
    host: str = Field(default="0.0.0.0", description="服务器监听地址")
    port: int = Field(default=8000, description="服务器监听端口")
    workers: int = Field(default=1, description="工作进程数量")
    
    # API配置
    api_prefix: str = Field(default="/api/v1", description="API路径前缀")
    docs_url: str = Field(default="/docs", description="API文档路径")
    redoc_url: str = Field(default="/redoc", description="ReDoc文档路径")
    openapi_url: str = Field(default="/openapi.json", description="OpenAPI规范路径")
    
    # CORS配置
    cors_origins: List[str] = Field(
        default=["http://localhost:3000", "http://localhost:8080"],
        description="允许的CORS源"
    )
    cors_methods: List[str] = Field(
        default=["GET", "POST", "PUT", "DELETE", "OPTIONS"],
        description="允许的CORS方法"
    )
    cors_headers: List[str] = Field(
        default=["*"],
        description="允许的CORS头"
    )
    
    # 文件上传配置
    max_upload_size: int = Field(default=10 * 1024 * 1024, description="最大上传文件大小（字节）")
    upload_path: str = Field(default="uploads", description="文件上传路径")
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = False


class Settings(BaseSettings):
    """主配置类，整合所有配置模块"""
    
    # 各模块配置
    database: DatabaseSettings = DatabaseSettings()
    security: SecuritySettings = SecuritySettings()
    data_source: DataSourceSettings = DataSourceSettings()
    trading: TradingSettings = TradingSettings()
    monitoring: MonitoringSettings = MonitoringSettings()
    app: ApplicationSettings = ApplicationSettings()
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = False
        
    def get_all_settings(self) -> Dict[str, Any]:
        """获取所有配置项"""
        return {
            "database": self.database.dict(),
            "security": self.security.dict(),
            "data_source": self.data_source.dict(),
            "trading": self.trading.dict(),
            "monitoring": self.monitoring.dict(),
            "app": self.app.dict(),
        }


# 全局配置实例
settings = Settings()

# 确保必要的目录存在
os.makedirs("logs", exist_ok=True)
os.makedirs(settings.app.upload_path, exist_ok=True)
