"""
安全管理模块

提供用户认证、权限管理、密码加密等安全功能。
"""

import logging
import hashlib
import secrets
import jwt
from datetime import datetime, timedelta
from typing import Optional, Dict, Any

logger = logging.getLogger(__name__)

# JWT配置
JWT_SECRET_KEY = "your-super-secret-jwt-key-change-in-production"
JWT_ALGORITHM = "HS256"
JWT_EXPIRATION_HOURS = 24


class SecurityManager:
    """安全管理器

    负责用户认证、权限验证、密码加密等安全相关功能。
    """

    def __init__(self):
        self._initialized = False
        self._secret_key = JWT_SECRET_KEY
        self._algorithm = JWT_ALGORITHM

    def initialize(self):
        """初始化安全管理器"""
        if self._initialized:
            return

        try:
            logger.info("安全管理器初始化完成")
            self._initialized = True
        except Exception as e:
            logger.error(f"安全管理器初始化失败: {e}")
            raise

    def hash_password(self, password: str) -> str:
        """密码哈希

        使用SHA-256和随机盐值对密码进行哈希处理。
        """
        # 生成随机盐值
        salt = secrets.token_hex(16)

        # 使用SHA-256哈希密码和盐值
        password_hash = hashlib.sha256((password + salt).encode()).hexdigest()

        # 返回盐值和哈希值的组合
        return f"{salt}:{password_hash}"

    def verify_password(self, password: str, hashed_password: str) -> bool:
        """验证密码

        验证明文密码是否与哈希密码匹配。
        """
        try:
            # 分离盐值和哈希值
            salt, stored_hash = hashed_password.split(':', 1)

            # 使用相同的盐值哈希输入密码
            password_hash = hashlib.sha256((password + salt).encode()).hexdigest()

            # 比较哈希值
            return password_hash == stored_hash

        except ValueError:
            # 如果格式不正确，尝试简单比较（向后兼容）
            return f"hashed_{password}" == hashed_password
        except Exception as e:
            logger.error(f"密码验证失败: {e}")
            return False

    def create_access_token(self, user_data: Dict[str, Any]) -> str:
        """创建JWT访问令牌"""
        try:
            # 设置令牌过期时间
            expire = datetime.utcnow() + timedelta(hours=JWT_EXPIRATION_HOURS)

            # 创建令牌载荷
            payload = {
                "user_id": str(user_data.get("id")),
                "username": user_data.get("username"),
                "email": user_data.get("email"),
                "is_superuser": user_data.get("is_superuser", False),
                "exp": expire,
                "iat": datetime.utcnow(),
                "type": "access"
            }

            # 生成JWT令牌
            token = jwt.encode(payload, self._secret_key, algorithm=self._algorithm)

            logger.info(f"为用户 {user_data.get('username')} 创建访问令牌")
            return token

        except Exception as e:
            logger.error(f"创建访问令牌失败: {e}")
            raise

    def verify_token(self, token: str) -> Optional[Dict[str, Any]]:
        """验证JWT令牌"""
        try:
            # 解码JWT令牌
            payload = jwt.decode(token, self._secret_key, algorithms=[self._algorithm])

            # 检查令牌类型
            if payload.get("type") != "access":
                logger.warning("无效的令牌类型")
                return None

            # 检查令牌是否过期
            if datetime.utcnow() > datetime.fromtimestamp(payload.get("exp", 0)):
                logger.warning("令牌已过期")
                return None

            return payload

        except jwt.ExpiredSignatureError:
            logger.warning("令牌已过期")
            return None
        except jwt.InvalidTokenError as e:
            logger.warning(f"无效的令牌: {e}")
            return None
        except Exception as e:
            logger.error(f"令牌验证失败: {e}")
            return None

    def authenticate_user(self, username: str, password: str) -> Optional[Dict[str, Any]]:
        """用户认证

        这是一个占位符实现，实际应用中需要从数据库查询用户信息。
        """
        logger.info(f"用户认证请求: {username}")

        # 模拟用户数据（实际应用中从数据库查询）
        mock_users = {
            "admin": {
                "id": "1",
                "username": "admin",
                "email": "<EMAIL>",
                "password_hash": self.hash_password("admin123"),
                "is_superuser": True,
                "is_active": True
            },
            "trader": {
                "id": "2",
                "username": "trader",
                "email": "<EMAIL>",
                "password_hash": self.hash_password("trader123"),
                "is_superuser": False,
                "is_active": True
            }
        }

        user = mock_users.get(username)
        if user and self.verify_password(password, user["password_hash"]):
            logger.info(f"用户认证成功: {username}")
            return user

        logger.warning(f"用户认证失败: {username}")
        return None

    def check_permission(self, user: Dict[str, Any], permission: str) -> bool:
        """检查用户权限"""
        # 超级用户拥有所有权限
        if user.get("is_superuser"):
            return True

        # 这里应该检查用户的角色权限
        # 简化实现，实际应用中需要查询用户角色和权限
        logger.info(f"权限检查: 用户 {user.get('username')} 请求权限 {permission}")
        return False

    def generate_verification_token(self, user_id: str) -> str:
        """生成邮箱验证令牌"""
        try:
            expire = datetime.utcnow() + timedelta(hours=24)
            payload = {
                "user_id": user_id,
                "type": "email_verification",
                "exp": expire,
                "iat": datetime.utcnow()
            }

            token = jwt.encode(payload, self._secret_key, algorithm=self._algorithm)
            logger.info(f"为用户 {user_id} 生成邮箱验证令牌")
            return token

        except Exception as e:
            logger.error(f"生成验证令牌失败: {e}")
            raise

    def verify_email_token(self, token: str) -> Optional[str]:
        """验证邮箱验证令牌"""
        try:
            payload = jwt.decode(token, self._secret_key, algorithms=[self._algorithm])

            if payload.get("type") != "email_verification":
                return None

            return payload.get("user_id")

        except (jwt.ExpiredSignatureError, jwt.InvalidTokenError):
            return None


# 全局安全管理器实例
security_manager = SecurityManager()


def get_current_user() -> Optional[Dict[str, Any]]:
    """获取当前用户信息的便捷函数"""
    # 这里是占位符实现，实际应用中需要从请求上下文获取
    return None


def hash_password(password: str) -> str:
    """密码哈希的便捷函数"""
    return security_manager.hash_password(password)


def verify_password(password: str, hashed_password: str) -> bool:
    """密码验证的便捷函数"""
    return security_manager.verify_password(password, hashed_password)


def create_access_token(user_data: Dict[str, Any]) -> str:
    """创建访问令牌的便捷函数"""
    return security_manager.create_access_token(user_data)


def verify_token(token: str) -> Optional[Dict[str, Any]]:
    """验证令牌的便捷函数"""
    return security_manager.verify_token(token)