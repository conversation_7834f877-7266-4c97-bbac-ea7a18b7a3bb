"""
日志管理模块

提供统一的日志管理功能，支持结构化日志、日志分类、日志轮转等特性。
所有日志信息都使用中文描述。
"""

import json
import logging
import logging.handlers
import os
import sys
from datetime import datetime
from typing import Any, Dict, Optional

import structlog
from structlog.stdlib import LoggerFactory

from ..config import settings


class ChineseJSONFormatter(logging.Formatter):
    """中文JSON格式化器"""
    
    def format(self, record: logging.LogRecord) -> str:
        """格式化日志记录为JSON格式"""
        log_entry = {
            "时间戳": datetime.fromtimestamp(record.created).isoformat() + "Z",
            "日志级别": record.levelname,
            "模块名称": record.name,
            "消息内容": record.getMessage(),
            "文件路径": record.pathname,
            "行号": record.lineno,
            "函数名": record.funcName,
        }
        
        # 添加异常信息
        if record.exc_info:
            log_entry["异常信息"] = self.formatException(record.exc_info)
        
        # 添加额外字段
        if hasattr(record, "extra_fields"):
            log_entry.update(record.extra_fields)
        
        return json.dumps(log_entry, ensure_ascii=False, indent=None)


class StructuredLogger:
    """结构化日志记录器"""
    
    def __init__(self, name: str):
        self.logger = structlog.get_logger(name)
        self.name = name
    
    def debug(self, message: str, **kwargs) -> None:
        """记录调试信息"""
        self.logger.debug(message, **kwargs)
    
    def info(self, message: str, **kwargs) -> None:
        """记录一般信息"""
        self.logger.info(message, **kwargs)
    
    def warning(self, message: str, **kwargs) -> None:
        """记录警告信息"""
        self.logger.warning(message, **kwargs)
    
    def error(self, message: str, **kwargs) -> None:
        """记录错误信息"""
        self.logger.error(message, **kwargs)
    
    def critical(self, message: str, **kwargs) -> None:
        """记录严重错误信息"""
        self.logger.critical(message, **kwargs)
    
    def log_user_action(
        self,
        user_id: int,
        action: str,
        resource: str,
        result: str = "成功",
        **kwargs
    ) -> None:
        """记录用户操作日志"""
        self.info(
            "用户操作记录",
            用户ID=user_id,
            操作类型=action,
            操作资源=resource,
            操作结果=result,
            **kwargs
        )
    
    def log_trading_action(
        self,
        user_id: int,
        symbol: str,
        action: str,
        quantity: Optional[int] = None,
        price: Optional[float] = None,
        **kwargs
    ) -> None:
        """记录交易操作日志"""
        log_data = {
            "用户ID": user_id,
            "股票代码": symbol,
            "交易动作": action,
        }
        
        if quantity is not None:
            log_data["交易数量"] = quantity
        if price is not None:
            log_data["交易价格"] = price
        
        log_data.update(kwargs)
        
        self.info("交易操作记录", **log_data)
    
    def log_strategy_event(
        self,
        strategy_id: int,
        strategy_name: str,
        event_type: str,
        **kwargs
    ) -> None:
        """记录策略事件日志"""
        self.info(
            "策略事件记录",
            策略ID=strategy_id,
            策略名称=strategy_name,
            事件类型=event_type,
            **kwargs
        )
    
    def log_risk_event(
        self,
        risk_type: str,
        risk_level: str,
        description: str,
        **kwargs
    ) -> None:
        """记录风险事件日志"""
        self.warning(
            "风险事件记录",
            风险类型=risk_type,
            风险级别=risk_level,
            风险描述=description,
            **kwargs
        )
    
    def log_system_event(
        self,
        event_type: str,
        component: str,
        status: str,
        **kwargs
    ) -> None:
        """记录系统事件日志"""
        self.info(
            "系统事件记录",
            事件类型=event_type,
            系统组件=component,
            运行状态=status,
            **kwargs
        )


def setup_logging() -> None:
    """设置日志系统"""
    
    # 创建日志目录
    log_dir = os.path.dirname(settings.monitoring.log_file_path)
    os.makedirs(log_dir, exist_ok=True)
    
    # 配置structlog
    structlog.configure(
        processors=[
            structlog.stdlib.filter_by_level,
            structlog.stdlib.add_logger_name,
            structlog.stdlib.add_log_level,
            structlog.stdlib.PositionalArgumentsFormatter(),
            structlog.processors.TimeStamper(fmt="iso"),
            structlog.processors.StackInfoRenderer(),
            structlog.processors.format_exc_info,
            structlog.processors.UnicodeDecoder(),
            structlog.processors.JSONRenderer(ensure_ascii=False),
        ],
        context_class=dict,
        logger_factory=LoggerFactory(),
        wrapper_class=structlog.stdlib.BoundLogger,
        cache_logger_on_first_use=True,
    )
    
    # 配置根日志记录器
    root_logger = logging.getLogger()
    root_logger.setLevel(getattr(logging, settings.monitoring.log_level.upper()))
    
    # 清除现有处理器
    for handler in root_logger.handlers[:]:
        root_logger.removeHandler(handler)
    
    # 控制台处理器
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setLevel(logging.INFO)
    
    if settings.monitoring.log_format == "json":
        console_formatter = ChineseJSONFormatter()
    else:
        console_formatter = logging.Formatter(
            "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
            datefmt="%Y-%m-%d %H:%M:%S"
        )
    
    console_handler.setFormatter(console_formatter)
    root_logger.addHandler(console_handler)
    
    # 文件处理器（应用日志）
    file_handler = logging.handlers.RotatingFileHandler(
        filename=settings.monitoring.log_file_path,
        maxBytes=_parse_size(settings.monitoring.log_max_size),
        backupCount=settings.monitoring.log_backup_count,
        encoding="utf-8"
    )
    file_handler.setLevel(logging.DEBUG)
    file_handler.setFormatter(ChineseJSONFormatter())
    root_logger.addHandler(file_handler)
    
    # 错误日志处理器
    error_handler = logging.handlers.RotatingFileHandler(
        filename=os.path.join(log_dir, "error.log"),
        maxBytes=_parse_size(settings.monitoring.log_max_size),
        backupCount=settings.monitoring.log_backup_count,
        encoding="utf-8"
    )
    error_handler.setLevel(logging.ERROR)
    error_handler.setFormatter(ChineseJSONFormatter())
    root_logger.addHandler(error_handler)
    
    # 交易日志处理器
    trading_logger = logging.getLogger("trading")
    trading_handler = logging.handlers.RotatingFileHandler(
        filename=os.path.join(log_dir, "trading.log"),
        maxBytes=_parse_size(settings.monitoring.log_max_size),
        backupCount=365,  # 交易日志保留一年
        encoding="utf-8"
    )
    trading_handler.setFormatter(ChineseJSONFormatter())
    trading_logger.addHandler(trading_handler)
    trading_logger.setLevel(logging.INFO)
    trading_logger.propagate = False
    
    # 审计日志处理器
    audit_logger = logging.getLogger("audit")
    audit_handler = logging.handlers.RotatingFileHandler(
        filename=os.path.join(log_dir, "audit.log"),
        maxBytes=_parse_size(settings.monitoring.log_max_size),
        backupCount=2555,  # 审计日志保留7年
        encoding="utf-8"
    )
    audit_handler.setFormatter(ChineseJSONFormatter())
    audit_logger.addHandler(audit_handler)
    audit_logger.setLevel(logging.INFO)
    audit_logger.propagate = False
    
    # 风险日志处理器
    risk_logger = logging.getLogger("risk")
    risk_handler = logging.handlers.RotatingFileHandler(
        filename=os.path.join(log_dir, "risk.log"),
        maxBytes=_parse_size(settings.monitoring.log_max_size),
        backupCount=365,
        encoding="utf-8"
    )
    risk_handler.setFormatter(ChineseJSONFormatter())
    risk_logger.addHandler(risk_handler)
    risk_logger.setLevel(logging.WARNING)
    risk_logger.propagate = False


def _parse_size(size_str: str) -> int:
    """解析大小字符串为字节数"""
    size_str = size_str.upper()
    if size_str.endswith("KB"):
        return int(size_str[:-2]) * 1024
    elif size_str.endswith("MB"):
        return int(size_str[:-2]) * 1024 * 1024
    elif size_str.endswith("GB"):
        return int(size_str[:-2]) * 1024 * 1024 * 1024
    else:
        return int(size_str)


def get_logger(name: str) -> StructuredLogger:
    """获取结构化日志记录器"""
    return StructuredLogger(name)


# 预定义的日志记录器
app_logger = get_logger("app")
trading_logger = get_logger("trading")
audit_logger = get_logger("audit")
risk_logger = get_logger("risk")
data_logger = get_logger("data")
strategy_logger = get_logger("strategy")
