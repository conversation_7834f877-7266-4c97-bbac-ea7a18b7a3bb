"""
异常处理模块

定义系统中使用的各种异常类型，提供统一的错误处理机制。
所有异常都包含详细的中文错误信息。
"""

from typing import Any, Dict, Optional


class QuantitativeToolsException(Exception):
    """量化工具系统基础异常类"""
    
    def __init__(
        self,
        message: str,
        error_code: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None,
    ):
        """
        初始化异常
        
        Args:
            message: 错误信息
            error_code: 错误代码
            details: 错误详细信息
        """
        self.message = message
        self.error_code = error_code or self.__class__.__name__.upper()
        self.details = details or {}
        super().__init__(self.message)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "error_code": self.error_code,
            "message": self.message,
            "details": self.details,
        }


class ValidationError(QuantitativeToolsException):
    """数据验证异常"""
    
    def __init__(
        self,
        message: str = "数据验证失败",
        field: Optional[str] = None,
        value: Any = None,
        constraint: Optional[str] = None,
    ):
        details = {}
        if field:
            details["field"] = field
        if value is not None:
            details["value"] = value
        if constraint:
            details["constraint"] = constraint
            
        super().__init__(
            message=message,
            error_code="VALIDATION_ERROR",
            details=details,
        )


class AuthenticationError(QuantitativeToolsException):
    """认证异常"""
    
    def __init__(self, message: str = "认证失败"):
        super().__init__(
            message=message,
            error_code="AUTHENTICATION_ERROR",
        )


class AuthorizationError(QuantitativeToolsException):
    """授权异常"""
    
    def __init__(
        self,
        message: str = "权限不足",
        required_permission: Optional[str] = None,
        user_permissions: Optional[list] = None,
    ):
        details = {}
        if required_permission:
            details["required_permission"] = required_permission
        if user_permissions:
            details["user_permissions"] = user_permissions
            
        super().__init__(
            message=message,
            error_code="AUTHORIZATION_ERROR",
            details=details,
        )


class DataNotFoundError(QuantitativeToolsException):
    """数据未找到异常"""
    
    def __init__(
        self,
        message: str = "数据不存在",
        resource_type: Optional[str] = None,
        resource_id: Any = None,
    ):
        details = {}
        if resource_type:
            details["resource_type"] = resource_type
        if resource_id is not None:
            details["resource_id"] = resource_id
            
        super().__init__(
            message=message,
            error_code="DATA_NOT_FOUND",
            details=details,
        )


class BusinessLogicError(QuantitativeToolsException):
    """业务逻辑异常"""
    
    def __init__(
        self,
        message: str = "业务逻辑错误",
        business_rule: Optional[str] = None,
    ):
        details = {}
        if business_rule:
            details["business_rule"] = business_rule
            
        super().__init__(
            message=message,
            error_code="BUSINESS_LOGIC_ERROR",
            details=details,
        )


class DatabaseError(QuantitativeToolsException):
    """数据库异常"""
    
    def __init__(
        self,
        message: str = "数据库操作失败",
        operation: Optional[str] = None,
        table: Optional[str] = None,
    ):
        details = {}
        if operation:
            details["operation"] = operation
        if table:
            details["table"] = table
            
        super().__init__(
            message=message,
            error_code="DATABASE_ERROR",
            details=details,
        )


class ExternalServiceError(QuantitativeToolsException):
    """外部服务异常"""
    
    def __init__(
        self,
        message: str = "外部服务调用失败",
        service_name: Optional[str] = None,
        status_code: Optional[int] = None,
        response_body: Optional[str] = None,
    ):
        details = {}
        if service_name:
            details["service_name"] = service_name
        if status_code:
            details["status_code"] = status_code
        if response_body:
            details["response_body"] = response_body
            
        super().__init__(
            message=message,
            error_code="EXTERNAL_SERVICE_ERROR",
            details=details,
        )


class ConfigurationError(QuantitativeToolsException):
    """配置异常"""
    
    def __init__(
        self,
        message: str = "配置错误",
        config_key: Optional[str] = None,
        config_value: Any = None,
    ):
        details = {}
        if config_key:
            details["config_key"] = config_key
        if config_value is not None:
            details["config_value"] = config_value
            
        super().__init__(
            message=message,
            error_code="CONFIGURATION_ERROR",
            details=details,
        )


class RateLimitError(QuantitativeToolsException):
    """频率限制异常"""
    
    def __init__(
        self,
        message: str = "请求频率超限",
        limit: Optional[int] = None,
        window: Optional[int] = None,
        retry_after: Optional[int] = None,
    ):
        details = {}
        if limit:
            details["limit"] = limit
        if window:
            details["window"] = window
        if retry_after:
            details["retry_after"] = retry_after
            
        super().__init__(
            message=message,
            error_code="RATE_LIMIT_ERROR",
            details=details,
        )


class StrategyError(QuantitativeToolsException):
    """策略异常"""
    
    def __init__(
        self,
        message: str = "策略执行错误",
        strategy_id: Optional[int] = None,
        strategy_name: Optional[str] = None,
        error_type: Optional[str] = None,
    ):
        details = {}
        if strategy_id:
            details["strategy_id"] = strategy_id
        if strategy_name:
            details["strategy_name"] = strategy_name
        if error_type:
            details["error_type"] = error_type
            
        super().__init__(
            message=message,
            error_code="STRATEGY_ERROR",
            details=details,
        )


class TradingError(QuantitativeToolsException):
    """交易异常"""
    
    def __init__(
        self,
        message: str = "交易执行错误",
        order_id: Optional[int] = None,
        symbol: Optional[str] = None,
        error_type: Optional[str] = None,
    ):
        details = {}
        if order_id:
            details["order_id"] = order_id
        if symbol:
            details["symbol"] = symbol
        if error_type:
            details["error_type"] = error_type
            
        super().__init__(
            message=message,
            error_code="TRADING_ERROR",
            details=details,
        )


class RiskManagementError(QuantitativeToolsException):
    """风险管理异常"""
    
    def __init__(
        self,
        message: str = "风险管理规则违反",
        rule_name: Optional[str] = None,
        risk_level: Optional[str] = None,
        current_value: Any = None,
        threshold_value: Any = None,
    ):
        details = {}
        if rule_name:
            details["rule_name"] = rule_name
        if risk_level:
            details["risk_level"] = risk_level
        if current_value is not None:
            details["current_value"] = current_value
        if threshold_value is not None:
            details["threshold_value"] = threshold_value
            
        super().__init__(
            message=message,
            error_code="RISK_MANAGEMENT_ERROR",
            details=details,
        )


class DataQualityError(QuantitativeToolsException):
    """数据质量异常"""
    
    def __init__(
        self,
        message: str = "数据质量检查失败",
        data_source: Optional[str] = None,
        quality_issue: Optional[str] = None,
        affected_records: Optional[int] = None,
    ):
        details = {}
        if data_source:
            details["data_source"] = data_source
        if quality_issue:
            details["quality_issue"] = quality_issue
        if affected_records:
            details["affected_records"] = affected_records
            
        super().__init__(
            message=message,
            error_code="DATA_QUALITY_ERROR",
            details=details,
        )
