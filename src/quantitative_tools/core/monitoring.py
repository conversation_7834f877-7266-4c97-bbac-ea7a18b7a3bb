"""
监控模块

提供应用性能监控、指标收集和健康检查功能。
"""

import time
import logging
import psutil
from typing import Dict, Any, Optional
from datetime import datetime
from functools import wraps

logger = logging.getLogger(__name__)

try:
    from prometheus_client import Counter, Histogram, Gauge, CollectorRegistry, generate_latest
    PROMETHEUS_AVAILABLE = True
except ImportError:
    # 如果prometheus_client不可用，创建占位符
    Counter = Histogram = Gauge = CollectorRegistry = None
    generate_latest = lambda x: b"# Prometheus not available"
    PROMETHEUS_AVAILABLE = False


class MetricsCollector:
    """指标收集器"""

    def __init__(self):
        self._initialized = False
        self._registry = None
        self._metrics = {}

        if PROMETHEUS_AVAILABLE:
            self._registry = CollectorRegistry()
            self._init_metrics()

    def _init_metrics(self):
        """初始化指标"""
        if not PROMETHEUS_AVAILABLE:
            return

        # HTTP请求指标
        self._metrics['http_requests_total'] = Counter(
            'http_requests_total',
            'HTTP请求总数',
            ['method', 'endpoint', 'status'],
            registry=self._registry
        )

        self._metrics['http_request_duration_seconds'] = Histogram(
            'http_request_duration_seconds',
            'HTTP请求持续时间',
            ['method', 'endpoint'],
            registry=self._registry
        )

        # 业务指标
        self._metrics['strategy_executions_total'] = Counter(
            'strategy_executions_total',
            '策略执行总数',
            ['strategy_id', 'status'],
            registry=self._registry
        )

        self._metrics['trading_orders_total'] = Counter(
            'trading_orders_total',
            '交易订单总数',
            ['symbol', 'side', 'status'],
            registry=self._registry
        )

        # 系统指标
        self._metrics['system_cpu_usage'] = Gauge(
            'system_cpu_usage',
            '系统CPU使用率',
            registry=self._registry
        )

        self._metrics['system_memory_usage'] = Gauge(
            'system_memory_usage',
            '系统内存使用率',
            registry=self._registry
        )

        self._initialized = True
        logger.info("指标收集器初始化完成")

    def record_http_request(self, method: str, endpoint: str, status: int, duration: float):
        """记录HTTP请求指标"""
        if not self._initialized:
            return

        self._metrics['http_requests_total'].labels(
            method=method,
            endpoint=endpoint,
            status=str(status)
        ).inc()

        self._metrics['http_request_duration_seconds'].labels(
            method=method,
            endpoint=endpoint
        ).observe(duration)

    def record_strategy_execution(self, strategy_id: str, status: str):
        """记录策略执行"""
        if not self._initialized:
            return

        self._metrics['strategy_executions_total'].labels(
            strategy_id=strategy_id,
            status=status
        ).inc()

    def update_system_metrics(self):
        """更新系统指标"""
        if not self._initialized:
            return

        try:
            # CPU使用率
            cpu_percent = psutil.cpu_percent(interval=1)
            self._metrics['system_cpu_usage'].set(cpu_percent)

            # 内存使用率
            memory = psutil.virtual_memory()
            self._metrics['system_memory_usage'].set(memory.percent)

        except Exception as e:
            logger.error(f"更新系统指标失败: {e}")

    def get_metrics(self) -> bytes:
        """获取Prometheus格式的指标"""
        if not self._initialized:
            return b"# Metrics not initialized"

        return generate_latest(self._registry)


class HealthChecker:
    """健康检查器"""

    def __init__(self):
        self._checks = {}

    def register_check(self, name: str, check_func):
        """注册健康检查函数"""
        self._checks[name] = check_func
        logger.info(f"注册健康检查: {name}")

    def check_health(self) -> Dict[str, Any]:
        """执行健康检查"""
        results = {
            "status": "healthy",
            "timestamp": datetime.now().isoformat(),
            "checks": {}
        }

        overall_healthy = True

        for name, check_func in self._checks.items():
            try:
                start_time = time.time()
                check_result = check_func()
                duration = time.time() - start_time

                if isinstance(check_result, bool):
                    check_result = {
                        "healthy": check_result,
                        "duration": duration
                    }
                elif isinstance(check_result, dict):
                    check_result["duration"] = duration
                else:
                    check_result = {
                        "healthy": bool(check_result),
                        "duration": duration,
                        "details": str(check_result)
                    }

                results["checks"][name] = check_result

                if not check_result.get("healthy", True):
                    overall_healthy = False

            except Exception as e:
                logger.error(f"健康检查失败 {name}: {e}")
                results["checks"][name] = {
                    "healthy": False,
                    "error": str(e),
                    "duration": 0
                }
                overall_healthy = False

        results["status"] = "healthy" if overall_healthy else "unhealthy"
        return results


def monitor_performance(operation: str = None):
    """性能监控装饰器"""
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            start_time = time.time()
            operation_name = operation or f"{func.__module__}.{func.__name__}"

            try:
                result = func(*args, **kwargs)
                duration = time.time() - start_time
                logger.debug(f"操作 {operation_name} 完成，耗时: {duration:.3f}s")
                return result

            except Exception as e:
                duration = time.time() - start_time
                logger.error(f"操作 {operation_name} 失败，耗时: {duration:.3f}s，错误: {e}")
                raise

        return wrapper
    return decorator


# 全局实例
metrics_collector = MetricsCollector()
health_checker = HealthChecker()


def get_system_info() -> Dict[str, Any]:
    """获取系统信息"""
    try:
        return {
            "cpu_count": psutil.cpu_count(),
            "cpu_percent": psutil.cpu_percent(interval=1),
            "memory": {
                "total": psutil.virtual_memory().total,
                "available": psutil.virtual_memory().available,
                "percent": psutil.virtual_memory().percent
            },
            "disk": {
                "total": psutil.disk_usage('/').total,
                "free": psutil.disk_usage('/').free,
                "percent": psutil.disk_usage('/').percent
            },
            "boot_time": psutil.boot_time()
        }
    except Exception as e:
        logger.error(f"获取系统信息失败: {e}")
        return {"error": str(e)}