"""
数据库管理模块

提供数据库连接、会话管理和基础操作功能。
支持PostgreSQL主数据库、ClickHouse时序数据库和Redis缓存。
"""

import logging
from contextlib import contextmanager
from typing import Generator, Optional

try:
    from sqlalchemy import create_engine, MetaData
    from sqlalchemy.ext.declarative import declarative_base
    from sqlalchemy.orm import sessionmaker, Session
    from sqlalchemy.pool import QueuePool
except ImportError:
    # 如果SQLAlchemy未安装，创建占位符
    create_engine = None
    MetaData = None
    declarative_base = None
    sessionmaker = None
    Session = None
    QueuePool = None

try:
    import redis
except ImportError:
    redis = None

try:
    from clickhouse_driver import Client as ClickHouseClient
except ImportError:
    ClickHouseClient = None

from ..config import settings

logger = logging.getLogger(__name__)

# SQLAlchemy基础配置
if declarative_base:
    Base = declarative_base()
    metadata = MetaData()
else:
    Base = None
    metadata = None


class DatabaseManager:
    """数据库管理器

    负责管理PostgreSQL、ClickHouse和Redis的连接和会话。
    """

    def __init__(self):
        self._pg_engine = None
        self._pg_session_factory = None
        self._clickhouse_client = None
        self._redis_client = None
        self._initialized = False

    def initialize(self):
        """初始化所有数据库连接"""
        if self._initialized:
            return

        try:
            # 初始化PostgreSQL连接
            if create_engine:
                self._init_postgresql()

            # 初始化ClickHouse连接
            if ClickHouseClient:
                self._init_clickhouse()

            # 初始化Redis连接
            if redis:
                self._init_redis()

            self._initialized = True
            logger.info("数据库管理器初始化完成")

        except Exception as e:
            logger.error(f"数据库初始化失败: {e}")
            # 在测试环境中不抛出异常
            if getattr(settings, 'ENVIRONMENT', 'development') != 'test':
                raise

    def _init_postgresql(self):
        """初始化PostgreSQL连接"""
        try:
            # 构建数据库URL
            db_url = (
                f"postgresql://{getattr(settings, 'POSTGRES_USER', 'postgres')}:"
                f"{getattr(settings, 'POSTGRES_PASSWORD', 'postgres')}"
                f"@{getattr(settings, 'POSTGRES_HOST', 'localhost')}:"
                f"{getattr(settings, 'POSTGRES_PORT', 5432)}/"
                f"{getattr(settings, 'POSTGRES_DB', 'quantitative_tools')}"
            )

            # 创建引擎
            self._pg_engine = create_engine(
                db_url,
                poolclass=QueuePool,
                pool_size=getattr(settings, 'POSTGRES_POOL_SIZE', 20),
                max_overflow=getattr(settings, 'POSTGRES_MAX_OVERFLOW', 30),
                pool_timeout=30,
                pool_recycle=3600,
                echo=getattr(settings, 'POSTGRES_ECHO', False)
            )

            # 创建会话工厂
            self._pg_session_factory = sessionmaker(
                bind=self._pg_engine,
                autocommit=False,
                autoflush=False
            )

            logger.info("PostgreSQL连接初始化成功")

        except Exception as e:
            logger.error(f"PostgreSQL初始化失败: {e}")
            raise

    def _init_clickhouse(self):
        """初始化ClickHouse连接"""
        try:
            self._clickhouse_client = ClickHouseClient(
                host=getattr(settings, 'CLICKHOUSE_HOST', 'localhost'),
                port=getattr(settings, 'CLICKHOUSE_PORT', 9000),
                user=getattr(settings, 'CLICKHOUSE_USER', 'default'),
                password=getattr(settings, 'CLICKHOUSE_PASSWORD', ''),
                database=getattr(settings, 'CLICKHOUSE_DB', 'market_data'),
                secure=getattr(settings, 'CLICKHOUSE_SECURE', False)
            )

            # 测试连接
            self._clickhouse_client.execute('SELECT 1')
            logger.info("ClickHouse连接初始化成功")

        except Exception as e:
            logger.error(f"ClickHouse初始化失败: {e}")
            raise

    def _init_redis(self):
        """初始化Redis连接"""
        try:
            self._redis_client = redis.Redis(
                host=getattr(settings, 'REDIS_HOST', 'localhost'),
                port=getattr(settings, 'REDIS_PORT', 6379),
                password=getattr(settings, 'REDIS_PASSWORD', None),
                db=getattr(settings, 'REDIS_DB', 0),
                decode_responses=True,
                socket_timeout=getattr(settings, 'REDIS_SOCKET_TIMEOUT', 5),
                socket_connect_timeout=getattr(settings, 'REDIS_SOCKET_CONNECT_TIMEOUT', 5),
                max_connections=getattr(settings, 'REDIS_POOL_SIZE', 20)
            )

            # 测试连接
            self._redis_client.ping()
            logger.info("Redis连接初始化成功")

        except Exception as e:
            logger.error(f"Redis初始化失败: {e}")
            raise

    @property
    def pg_engine(self):
        """获取PostgreSQL引擎"""
        if not self._initialized:
            self.initialize()
        return self._pg_engine

    @property
    def clickhouse_client(self):
        """获取ClickHouse客户端"""
        if not self._initialized:
            self.initialize()
        return self._clickhouse_client

    @property
    def redis_client(self):
        """获取Redis客户端"""
        if not self._initialized:
            self.initialize()
        return self._redis_client

    def get_pg_session(self):
        """获取PostgreSQL会话"""
        if not self._initialized:
            self.initialize()
        if self._pg_session_factory:
            return self._pg_session_factory()
        return None

    @contextmanager
    def get_pg_session_context(self):
        """获取PostgreSQL会话上下文管理器"""
        session = self.get_pg_session()
        if session:
            try:
                yield session
                session.commit()
            except Exception:
                session.rollback()
                raise
            finally:
                session.close()
        else:
            yield None

    def close_all(self):
        """关闭所有数据库连接"""
        try:
            if self._pg_engine:
                self._pg_engine.dispose()
                logger.info("PostgreSQL连接已关闭")

            if self._clickhouse_client:
                self._clickhouse_client.disconnect()
                logger.info("ClickHouse连接已关闭")

            if self._redis_client:
                self._redis_client.close()
                logger.info("Redis连接已关闭")

            self._initialized = False

        except Exception as e:
            logger.error(f"关闭数据库连接时出错: {e}")


# 全局数据库管理器实例
db_manager = DatabaseManager()


def get_db_session():
    """获取数据库会话的便捷函数"""
    return db_manager.get_pg_session()


@contextmanager
def get_db_session_context():
    """获取数据库会话上下文管理器的便捷函数"""
    with db_manager.get_pg_session_context() as session:
        yield session


def get_clickhouse_client():
    """获取ClickHouse客户端的便捷函数"""
    return db_manager.clickhouse_client


def get_redis_client():
    """获取Redis客户端的便捷函数"""
    return db_manager.redis_client