"""
订单管理系统

提供完整的订单生命周期管理，包括：
- 订单创建和验证
- 订单状态管理
- 订单路由和执行
- 订单撤销和修改
- 订单历史记录
"""

import logging
import uuid
from typing import Dict, List, Optional, Any
from datetime import datetime
from dataclasses import dataclass, field
from enum import Enum

logger = logging.getLogger(__name__)


class OrderType(Enum):
    """订单类型"""
    MARKET = "market"      # 市价单
    LIMIT = "limit"        # 限价单


class OrderSide(Enum):
    """订单方向"""
    BUY = "buy"
    SELL = "sell"


class OrderStatus(Enum):
    """订单状态"""
    PENDING = "pending"           # 待处理
    SUBMITTED = "submitted"       # 已提交
    FILLED = "filled"            # 完全成交
    CANCELLED = "cancelled"      # 已撤销
    REJECTED = "rejected"        # 已拒绝


@dataclass
class Order:
    """订单对象"""
    order_id: str = field(default_factory=lambda: str(uuid.uuid4()))
    symbol: str = ""
    side: OrderSide = OrderSide.BUY
    order_type: OrderType = OrderType.MARKET
    quantity: float = 0.0
    price: Optional[float] = None

    # 状态信息
    status: OrderStatus = OrderStatus.PENDING
    filled_quantity: float = 0.0
    avg_fill_price: float = 0.0

    # 时间信息
    created_at: datetime = field(default_factory=datetime.now)
    updated_at: datetime = field(default_factory=datetime.now)

    # 策略信息
    strategy_id: Optional[str] = None

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'order_id': self.order_id,
            'symbol': self.symbol,
            'side': self.side.value,
            'order_type': self.order_type.value,
            'quantity': self.quantity,
            'price': self.price,
            'status': self.status.value,
            'filled_quantity': self.filled_quantity,
            'avg_fill_price': self.avg_fill_price,
            'created_at': self.created_at.isoformat(),
            'updated_at': self.updated_at.isoformat(),
            'strategy_id': self.strategy_id
        }


class OrderManager:
    """订单管理器"""

    def __init__(self):
        self._orders: Dict[str, Order] = {}
        self._order_callbacks: List[callable] = []

    def create_order(self, symbol: str, side: OrderSide, quantity: float,
                    order_type: OrderType = OrderType.MARKET,
                    price: Optional[float] = None,
                    strategy_id: Optional[str] = None) -> Order:
        """创建订单"""
        try:
            order = Order(
                symbol=symbol,
                side=side,
                order_type=order_type,
                quantity=quantity,
                price=price,
                strategy_id=strategy_id
            )

            # 验证订单
            if not self._validate_order(order):
                order.status = OrderStatus.REJECTED
                return order

            # 存储订单
            self._orders[order.order_id] = order

            logger.info(f"创建订单: {order.order_id} {symbol} {side.value} {quantity}")
            return order

        except Exception as e:
            logger.error(f"创建订单失败: {e}")
            raise

    def submit_order(self, order_id: str) -> bool:
        """提交订单"""
        try:
            order = self._orders.get(order_id)
            if not order:
                return False

            if order.status != OrderStatus.PENDING:
                return False

            # 模拟提交成功
            order.status = OrderStatus.SUBMITTED
            order.updated_at = datetime.now()

            # 模拟立即成交（简化）
            self._simulate_fill(order)

            logger.info(f"订单提交成功: {order_id}")
            return True

        except Exception as e:
            logger.error(f"提交订单失败: {e}")
            return False

    def cancel_order(self, order_id: str) -> bool:
        """撤销订单"""
        try:
            order = self._orders.get(order_id)
            if not order:
                return False

            if order.status in [OrderStatus.FILLED, OrderStatus.CANCELLED]:
                return False

            order.status = OrderStatus.CANCELLED
            order.updated_at = datetime.now()

            logger.info(f"订单撤销成功: {order_id}")
            return True

        except Exception as e:
            logger.error(f"撤销订单失败: {e}")
            return False

    def get_order(self, order_id: str) -> Optional[Order]:
        """获取订单"""
        return self._orders.get(order_id)

    def list_orders(self, status: Optional[OrderStatus] = None) -> List[Order]:
        """列出订单"""
        orders = list(self._orders.values())

        if status:
            orders = [o for o in orders if o.status == status]

        return sorted(orders, key=lambda x: x.created_at, reverse=True)

    def add_callback(self, callback: callable):
        """添加订单状态变化回调"""
        self._order_callbacks.append(callback)

    def _validate_order(self, order: Order) -> bool:
        """验证订单"""
        if not order.symbol or order.quantity <= 0:
            return False

        if order.order_type == OrderType.LIMIT and not order.price:
            return False

        return True

    def _simulate_fill(self, order: Order):
        """模拟订单成交"""
        import random

        # 模拟成交价格
        if order.order_type == OrderType.MARKET:
            fill_price = 10.0 + random.uniform(-0.5, 0.5)
        else:
            fill_price = order.price

        order.filled_quantity = order.quantity
        order.avg_fill_price = fill_price
        order.status = OrderStatus.FILLED
        order.updated_at = datetime.now()


# 全局订单管理器实例
order_manager = OrderManager()