"""
仓位管理系统

提供完整的仓位计算和管理功能，包括：
- 实时仓位计算
- 持仓成本分析
- 盈亏计算
- 仓位风险评估
- 资金管理
"""

import logging
from typing import Dict, List, Optional, Any
from datetime import datetime
from dataclasses import dataclass, field

logger = logging.getLogger(__name__)


@dataclass
class Position:
    """仓位对象"""
    symbol: str
    quantity: float = 0.0
    avg_cost: float = 0.0
    market_value: float = 0.0
    unrealized_pnl: float = 0.0
    realized_pnl: float = 0.0
    last_price: float = 0.0
    updated_at: datetime = field(default_factory=datetime.now)

    @property
    def total_cost(self) -> float:
        """总成本"""
        return abs(self.quantity) * self.avg_cost

    @property
    def pnl_percent(self) -> float:
        """盈亏百分比"""
        if self.total_cost == 0:
            return 0.0
        return (self.unrealized_pnl / self.total_cost) * 100

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'symbol': self.symbol,
            'quantity': self.quantity,
            'avg_cost': self.avg_cost,
            'market_value': self.market_value,
            'unrealized_pnl': self.unrealized_pnl,
            'realized_pnl': self.realized_pnl,
            'last_price': self.last_price,
            'total_cost': self.total_cost,
            'pnl_percent': self.pnl_percent,
            'updated_at': self.updated_at.isoformat()
        }


class PositionManager:
    """仓位管理器"""

    def __init__(self, initial_cash: float = 1000000.0):
        self._positions: Dict[str, Position] = {}
        self._cash: float = initial_cash
        self._initial_cash: float = initial_cash
        self._total_commission: float = 0.0

    def update_trade(self, symbol: str, quantity: float, price: float,
                    side: str, commission: float = 0.0):
        """更新交易到仓位"""
        try:
            if symbol not in self._positions:
                self._positions[symbol] = Position(symbol=symbol)

            position = self._positions[symbol]

            if side.lower() == 'buy':
                self._handle_buy(position, quantity, price, commission)
            else:
                self._handle_sell(position, quantity, price, commission)

            position.updated_at = datetime.now()

            logger.info(f"更新仓位: {symbol} {side} {quantity}@{price}")

        except Exception as e:
            logger.error(f"更新仓位失败: {e}")

    def _handle_buy(self, position: Position, quantity: float, price: float, commission: float):
        """处理买入"""
        # 计算新的平均成本
        total_quantity = position.quantity + quantity
        if total_quantity != 0:
            total_cost = position.quantity * position.avg_cost + quantity * price
            position.avg_cost = total_cost / total_quantity
        else:
            position.avg_cost = price

        position.quantity = total_quantity

        # 更新现金
        self._cash -= quantity * price + commission
        self._total_commission += commission

    def _handle_sell(self, position: Position, quantity: float, price: float, commission: float):
        """处理卖出"""
        if position.quantity >= quantity:
            # 计算已实现盈亏
            realized_pnl = (price - position.avg_cost) * quantity - commission
            position.realized_pnl += realized_pnl

            # 更新仓位
            position.quantity -= quantity

            # 更新现金
            self._cash += quantity * price - commission
            self._total_commission += commission

            # 如果仓位清零，重置平均成本
            if position.quantity == 0:
                position.avg_cost = 0.0
        else:
            logger.warning(f"卖出数量超过持仓: {position.symbol}")

    def update_market_price(self, symbol: str, price: float):
        """更新市场价格"""
        if symbol in self._positions:
            position = self._positions[symbol]
            position.last_price = price

            # 计算市值和未实现盈亏
            if position.quantity != 0:
                position.market_value = position.quantity * price
                position.unrealized_pnl = (price - position.avg_cost) * position.quantity
            else:
                position.market_value = 0.0
                position.unrealized_pnl = 0.0

            position.updated_at = datetime.now()

    def get_position(self, symbol: str) -> Optional[Position]:
        """获取仓位"""
        return self._positions.get(symbol)

    def list_positions(self, include_zero: bool = False) -> List[Position]:
        """列出所有仓位"""
        positions = list(self._positions.values())

        if not include_zero:
            positions = [p for p in positions if p.quantity != 0]

        return positions

    def get_portfolio_value(self) -> float:
        """获取组合总价值"""
        market_value = sum(p.market_value for p in self._positions.values())
        return self._cash + market_value

    def get_cash(self) -> float:
        """获取现金余额"""
        return self._cash

    def get_total_pnl(self) -> float:
        """获取总盈亏"""
        unrealized_pnl = sum(p.unrealized_pnl for p in self._positions.values())
        realized_pnl = sum(p.realized_pnl for p in self._positions.values())
        return unrealized_pnl + realized_pnl

    def get_portfolio_stats(self) -> Dict[str, Any]:
        """获取组合统计信息"""
        portfolio_value = self.get_portfolio_value()
        total_pnl = self.get_total_pnl()

        return {
            'portfolio_value': portfolio_value,
            'cash': self._cash,
            'initial_cash': self._initial_cash,
            'total_pnl': total_pnl,
            'total_return': (portfolio_value - self._initial_cash) / self._initial_cash * 100,
            'total_commission': self._total_commission,
            'positions_count': len([p for p in self._positions.values() if p.quantity != 0]),
            'market_value': sum(p.market_value for p in self._positions.values()),
            'unrealized_pnl': sum(p.unrealized_pnl for p in self._positions.values()),
            'realized_pnl': sum(p.realized_pnl for p in self._positions.values())
        }

    def calculate_position_risk(self, symbol: str) -> Dict[str, float]:
        """计算仓位风险"""
        position = self.get_position(symbol)
        if not position or position.quantity == 0:
            return {}

        portfolio_value = self.get_portfolio_value()
        position_weight = position.market_value / portfolio_value if portfolio_value > 0 else 0

        return {
            'position_weight': position_weight * 100,  # 百分比
            'concentration_risk': position_weight,
            'market_value': position.market_value,
            'unrealized_pnl': position.unrealized_pnl,
            'pnl_percent': position.pnl_percent
        }


# 全局仓位管理器实例
position_manager = PositionManager()