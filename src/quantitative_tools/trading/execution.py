"""
交易执行引擎

提供统一的交易执行接口，包括：
- 交易信号处理
- 订单生成和提交
- 执行结果处理
- 风险控制集成
- 执行统计分析
"""

import logging
from typing import Dict, List, Optional, Any, Callable
from datetime import datetime

from .orders import OrderManager, Order, OrderType, OrderSide, OrderStatus, order_manager
from .positions import PositionManager, position_manager

logger = logging.getLogger(__name__)


class TradeExecutor:
    """交易执行器"""

    def __init__(self, order_manager: OrderManager = None,
                 position_manager: PositionManager = None):
        self.order_manager = order_manager or order_manager
        self.position_manager = position_manager or position_manager

        # 执行配置
        self.commission_rate = 0.0003  # 手续费率
        self.min_commission = 5.0      # 最小手续费

        # 回调函数
        self._execution_callbacks: List[Callable] = []

        # 统计信息
        self._execution_stats = {
            'total_trades': 0,
            'successful_trades': 0,
            'failed_trades': 0,
            'total_commission': 0.0
        }

    def execute_signal(self, signal: Dict[str, Any]) -> Optional[Order]:
        """执行交易信号"""
        try:
            symbol = signal.get('symbol')
            action = signal.get('action', signal.get('signal_type', ''))
            quantity = signal.get('quantity', 100)
            price = signal.get('price')
            strategy_id = signal.get('strategy_id')

            if not symbol or not action:
                logger.error(f"信号参数不完整: {signal}")
                return None

            # 转换信号类型
            if action.lower() in ['buy', 'long']:
                side = OrderSide.BUY
            elif action.lower() in ['sell', 'short']:
                side = OrderSide.SELL
            else:
                logger.error(f"不支持的信号类型: {action}")
                return None

            # 确定订单类型
            order_type = OrderType.LIMIT if price else OrderType.MARKET

            # 风险检查
            if not self._risk_check(symbol, side, quantity, price):
                logger.warning(f"风险检查失败: {symbol} {action} {quantity}")
                return None

            # 创建订单
            order = self.order_manager.create_order(
                symbol=symbol,
                side=side,
                quantity=quantity,
                order_type=order_type,
                price=price,
                strategy_id=strategy_id
            )

            if order.status == OrderStatus.REJECTED:
                logger.error(f"订单创建失败: {order.error_message}")
                self._execution_stats['failed_trades'] += 1
                return order

            # 提交订单
            success = self.order_manager.submit_order(order.order_id)

            if success:
                logger.info(f"交易信号执行成功: {symbol} {action} {quantity}")
                self._execution_stats['successful_trades'] += 1

                # 注册订单回调以更新仓位
                self._register_order_callback(order)

            else:
                logger.error(f"订单提交失败: {order.order_id}")
                self._execution_stats['failed_trades'] += 1

            self._execution_stats['total_trades'] += 1

            # 通知回调
            self._notify_callbacks(order, signal)

            return order

        except Exception as e:
            logger.error(f"执行交易信号失败: {e}")
            self._execution_stats['failed_trades'] += 1
            return None

    def execute_batch_signals(self, signals: List[Dict[str, Any]]) -> List[Optional[Order]]:
        """批量执行交易信号"""
        orders = []

        for signal in signals:
            order = self.execute_signal(signal)
            orders.append(order)

        logger.info(f"批量执行完成: {len(signals)} 个信号, {len([o for o in orders if o])} 个订单")
        return orders

    def _risk_check(self, symbol: str, side: OrderSide, quantity: float, price: Optional[float]) -> bool:
        """风险检查"""
        try:
            # 检查现金是否足够
            if side == OrderSide.BUY:
                estimated_cost = quantity * (price or 10.0)  # 使用价格或默认价格估算
                commission = max(estimated_cost * self.commission_rate, self.min_commission)
                total_cost = estimated_cost + commission

                if self.position_manager.get_cash() < total_cost:
                    logger.warning(f"现金不足: 需要 {total_cost}, 可用 {self.position_manager.get_cash()}")
                    return False

            # 检查持仓是否足够卖出
            elif side == OrderSide.SELL:
                position = self.position_manager.get_position(symbol)
                if not position or position.quantity < quantity:
                    available = position.quantity if position else 0
                    logger.warning(f"持仓不足: 需要 {quantity}, 可用 {available}")
                    return False

            # 检查单笔交易限额
            portfolio_value = self.position_manager.get_portfolio_value()
            trade_value = quantity * (price or 10.0)
            trade_ratio = trade_value / portfolio_value if portfolio_value > 0 else 0

            if trade_ratio > 0.2:  # 单笔交易不超过组合的20%
                logger.warning(f"单笔交易金额过大: {trade_ratio:.2%}")
                return False

            return True

        except Exception as e:
            logger.error(f"风险检查异常: {e}")
            return False

    def _register_order_callback(self, order: Order):
        """注册订单回调以更新仓位"""
        def update_position_callback(updated_order: Order, event: str):
            if event == "filled" and updated_order.order_id == order.order_id:
                # 计算手续费
                commission = max(
                    updated_order.filled_quantity * updated_order.avg_fill_price * self.commission_rate,
                    self.min_commission
                )

                # 更新仓位
                self.position_manager.update_trade(
                    symbol=updated_order.symbol,
                    quantity=updated_order.filled_quantity,
                    price=updated_order.avg_fill_price,
                    side=updated_order.side.value,
                    commission=commission
                )

                self._execution_stats['total_commission'] += commission

                logger.info(f"仓位更新完成: {updated_order.symbol}")

        self.order_manager.add_callback(update_position_callback)

    def add_execution_callback(self, callback: Callable):
        """添加执行回调"""
        self._execution_callbacks.append(callback)

    def _notify_callbacks(self, order: Order, signal: Dict[str, Any]):
        """通知执行回调"""
        for callback in self._execution_callbacks:
            try:
                callback(order, signal)
            except Exception as e:
                logger.error(f"执行回调失败: {e}")

    def get_execution_stats(self) -> Dict[str, Any]:
        """获取执行统计"""
        stats = self._execution_stats.copy()

        if stats['total_trades'] > 0:
            stats['success_rate'] = stats['successful_trades'] / stats['total_trades'] * 100
        else:
            stats['success_rate'] = 0.0

        return stats

    def set_commission_rate(self, rate: float, min_commission: float = None):
        """设置手续费率"""
        self.commission_rate = rate
        if min_commission is not None:
            self.min_commission = min_commission

        logger.info(f"手续费率设置: {rate:.4f}, 最小手续费: {self.min_commission}")


# 全局交易执行器实例
trade_executor = TradeExecutor()