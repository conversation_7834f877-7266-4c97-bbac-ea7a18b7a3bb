# AI量化交易工具系统环境配置文件
# 复制此文件为 .env 并根据实际环境修改配置值

# =============================================================================
# 应用基础配置
# =============================================================================
APP_NAME=AI量化交易工具
APP_VERSION=1.0.0
DEBUG=false
ENVIRONMENT=production
HOST=0.0.0.0
PORT=8000
WORKERS=4

# API配置
API_PREFIX=/api/v1
DOCS_URL=/docs
REDOC_URL=/redoc
OPENAPI_URL=/openapi.json

# CORS配置
CORS_ORIGINS=["http://localhost:3000","http://localhost:8080","https://your-frontend-domain.com"]
CORS_METHODS=["GET","POST","PUT","DELETE","OPTIONS"]
CORS_HEADERS=["*"]

# 文件上传配置
MAX_UPLOAD_SIZE=10485760  # 10MB
UPLOAD_PATH=uploads

# =============================================================================
# 数据库配置
# =============================================================================

# PostgreSQL主数据库
POSTGRES_HOST=localhost
POSTGRES_PORT=5432
POSTGRES_USER=postgres
POSTGRES_PASSWORD=your_postgres_password
POSTGRES_DB=quantitative_tools

# ClickHouse时序数据库
CLICKHOUSE_HOST=localhost
CLICKHOUSE_PORT=9000
CLICKHOUSE_USER=default
CLICKHOUSE_PASSWORD=your_clickhouse_password
CLICKHOUSE_DB=market_data

# Redis缓存数据库
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=your_redis_password
REDIS_DB=0

# =============================================================================
# 安全配置
# =============================================================================

# JWT配置 - 生产环境必须修改密钥
SECRET_KEY=your-super-secret-key-change-in-production-min-32-chars
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=15
REFRESH_TOKEN_EXPIRE_DAYS=7

# 密码策略
PASSWORD_MIN_LENGTH=8
PASSWORD_REQUIRE_UPPERCASE=true
PASSWORD_REQUIRE_LOWERCASE=true
PASSWORD_REQUIRE_NUMBERS=true
PASSWORD_REQUIRE_SPECIAL=true

# API限流
RATE_LIMIT_PER_MINUTE=1000
RATE_LIMIT_PER_HOUR=10000

# 会话管理
SESSION_TIMEOUT_MINUTES=30
MAX_LOGIN_ATTEMPTS=5
LOCKOUT_DURATION_MINUTES=15

# =============================================================================
# 数据源配置
# =============================================================================

# Wind数据源
WIND_ENABLED=false
WIND_USERNAME=your_wind_username
WIND_PASSWORD=your_wind_password

# 东方财富数据源
EASTMONEY_ENABLED=true
EASTMONEY_TOKEN=your_eastmoney_token

# Tushare数据源
TUSHARE_ENABLED=true
TUSHARE_TOKEN=your_tushare_token

# 数据更新配置
REALTIME_DATA_INTERVAL=1
DAILY_DATA_UPDATE_TIME=18:00
DATA_RETENTION_DAYS=365

# 数据质量配置
DATA_QUALITY_CHECK_ENABLED=true
MAX_MISSING_DATA_RATIO=0.05
OUTLIER_DETECTION_ENABLED=true

# =============================================================================
# 交易配置
# =============================================================================

# 交易时间
MARKET_OPEN_TIME=09:30
MARKET_CLOSE_TIME=15:00
TRADING_DAYS_ONLY=true

# 订单配置
DEFAULT_ORDER_TIMEOUT=300
MAX_ORDER_SIZE=1000000
MIN_ORDER_SIZE=100

# 风控配置
MAX_POSITION_RATIO=0.1
MAX_DAILY_LOSS_RATIO=0.05
STOP_LOSS_RATIO=0.1

# 交易成本
COMMISSION_RATE=0.0003
STAMP_TAX_RATE=0.001
TRANSFER_FEE_RATE=0.00002
SLIPPAGE_RATE=0.001

# =============================================================================
# 监控和日志配置
# =============================================================================

# 日志配置
LOG_LEVEL=INFO
LOG_FORMAT=json
LOG_FILE_PATH=logs/app.log
LOG_MAX_SIZE=100MB
LOG_BACKUP_COUNT=10

# 监控配置
METRICS_ENABLED=true
METRICS_PORT=8080
HEALTH_CHECK_INTERVAL=30

# 告警配置
ALERT_ENABLED=true
ALERT_EMAIL_ENABLED=true
ALERT_SMS_ENABLED=false
ALERT_WEBHOOK_ENABLED=false

# 邮件配置
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your_email_password
SMTP_USE_TLS=true

# =============================================================================
# 第三方服务配置
# =============================================================================

# 短信服务（阿里云短信）
SMS_ACCESS_KEY_ID=your_sms_access_key
SMS_ACCESS_KEY_SECRET=your_sms_access_secret
SMS_SIGN_NAME=量化交易工具
SMS_TEMPLATE_CODE=SMS_123456789

# 对象存储（MinIO/阿里云OSS）
OSS_ENDPOINT=your_oss_endpoint
OSS_ACCESS_KEY_ID=your_oss_access_key
OSS_ACCESS_KEY_SECRET=your_oss_access_secret
OSS_BUCKET_NAME=quantitative-tools

# 消息队列（Kafka）
KAFKA_BOOTSTRAP_SERVERS=localhost:9092
KAFKA_SECURITY_PROTOCOL=PLAINTEXT
KAFKA_SASL_MECHANISM=PLAIN
KAFKA_SASL_USERNAME=your_kafka_username
KAFKA_SASL_PASSWORD=your_kafka_password

# =============================================================================
# 券商接口配置
# =============================================================================

# 华泰证券
HUATAI_ENABLED=false
HUATAI_APP_ID=your_huatai_app_id
HUATAI_APP_SECRET=your_huatai_app_secret
HUATAI_REDIRECT_URI=your_redirect_uri

# 中信证券
CITIC_ENABLED=false
CITIC_USERNAME=your_citic_username
CITIC_PASSWORD=your_citic_password
CITIC_TRADING_PASSWORD=your_trading_password

# 国泰君安
GTJA_ENABLED=false
GTJA_USERNAME=your_gtja_username
GTJA_PASSWORD=your_gtja_password

# =============================================================================
# 开发和测试配置
# =============================================================================

# 测试数据库（仅开发环境）
TEST_POSTGRES_DB=quantitative_tools_test
TEST_REDIS_DB=1

# 模拟交易配置
PAPER_TRADING_ENABLED=true
PAPER_TRADING_INITIAL_CAPITAL=1000000
PAPER_TRADING_COMMISSION_RATE=0.0003

# 开发工具配置
ENABLE_PROFILER=false
ENABLE_DEBUG_TOOLBAR=false
ENABLE_QUERY_LOGGING=false

# =============================================================================
# 性能优化配置
# =============================================================================

# 数据库连接池
DB_POOL_SIZE=20
DB_MAX_OVERFLOW=30
DB_POOL_TIMEOUT=30
DB_POOL_RECYCLE=3600

# Redis连接池
REDIS_POOL_SIZE=20
REDIS_POOL_TIMEOUT=5

# 缓存配置
CACHE_DEFAULT_TIMEOUT=300
CACHE_KEY_PREFIX=qt:
CACHE_SERIALIZER=json

# 异步任务配置
CELERY_BROKER_URL=redis://localhost:6379/2
CELERY_RESULT_BACKEND=redis://localhost:6379/3
CELERY_TASK_SERIALIZER=json
CELERY_RESULT_SERIALIZER=json
