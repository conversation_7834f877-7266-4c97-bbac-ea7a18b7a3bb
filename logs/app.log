{"时间戳": "2025-08-26T04:45:39.311552Z", "日志级别": "INFO", "模块名称": "integration_test", "消息内容": "{\"测试状态\": \"通过\", \"event\": \"系统集成测试\", \"logger\": \"integration_test\", \"level\": \"info\", \"timestamp\": \"2025-08-26T04:45:39.311505Z\"}", "文件路径": "/root/workspace/git.atjog.com/aier/ai-quantitative-tools/src/quantitative_tools/core/logging.py", "行号": 61, "函数名": "info"}
{"时间戳": "2025-08-26T04:45:39.312271Z", "日志级别": "ERROR", "模块名称": "error_test", "消息内容": "{\"错误信息\": \"集成测试错误\", \"错误代码\": \"VALIDATION_ERROR\", \"event\": \"捕获到验证错误\", \"logger\": \"error_test\", \"level\": \"error\", \"timestamp\": \"2025-08-26T04:45:39.312244Z\"}", "文件路径": "/root/workspace/git.atjog.com/aier/ai-quantitative-tools/src/quantitative_tools/core/logging.py", "行号": 69, "函数名": "error"}
