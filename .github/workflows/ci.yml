# AI量化交易工具系统 CI/CD流水线配置

name: CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]

env:
  PYTHON_VERSION: '3.9'
  NODE_VERSION: '18'

jobs:
  # 代码质量检查
  code-quality:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v3

    - name: 设置Python环境
      uses: actions/setup-python@v4
      with:
        python-version: ${{ env.PYTHON_VERSION }}

    - name: 安装依赖
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
        pip install black flake8 mypy pytest-cov

    - name: 代码格式检查 (Black)
      run: black --check src/ tests/

    - name: 代码风格检查 (Flake8)
      run: flake8 src/ tests/ --max-line-length=88 --extend-ignore=E203,W503

    - name: 类型检查 (MyPy)
      run: mypy src/ --ignore-missing-imports

    - name: 安全检查 (Bandit)
      run: |
        pip install bandit
        bandit -r src/ -f json -o bandit-report.json || true

    - name: 上传安全检查报告
      uses: actions/upload-artifact@v3
      with:
        name: security-report
        path: bandit-report.json

  # 单元测试
  unit-tests:
    runs-on: ubuntu-latest
    needs: code-quality

    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: postgres123
          POSTGRES_DB: quantitative_tools_test
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432

      redis:
        image: redis:7-alpine
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 6379:6379

    steps:
    - uses: actions/checkout@v3

    - name: 设置Python环境
      uses: actions/setup-python@v4
      with:
        python-version: ${{ env.PYTHON_VERSION }}

    - name: 安装依赖
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
        pip install pytest pytest-cov pytest-asyncio

    - name: 运行单元测试
      env:
        POSTGRES_HOST: localhost
        POSTGRES_PORT: 5432
        POSTGRES_USER: postgres
        POSTGRES_PASSWORD: postgres123
        POSTGRES_DB: quantitative_tools_test
        REDIS_HOST: localhost
        REDIS_PORT: 6379
        ENVIRONMENT: test
      run: |
        pytest tests/ -v --cov=src --cov-report=xml --cov-report=html

    - name: 上传测试覆盖率报告
      uses: codecov/codecov-action@v3
      with:
        file: ./coverage.xml
        flags: unittests
        name: codecov-umbrella

  # 集成测试
  integration-tests:
    runs-on: ubuntu-latest
    needs: unit-tests

    steps:
    - uses: actions/checkout@v3

    - name: 启动完整测试环境
      run: |
        docker-compose -f docker-compose.test.yml up -d
        sleep 30  # 等待服务启动

    - name: 运行集成测试
      run: |
        docker-compose -f docker-compose.test.yml exec -T app pytest tests/integration/ -v

    - name: 清理测试环境
      if: always()
      run: |
        docker-compose -f docker-compose.test.yml down -v