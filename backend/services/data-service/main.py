"""
数据服务

提供统一的数据访问API，包括：
- 实时行情数据查询
- 历史数据查询
- K线数据接口
- WebSocket实时推送
- 数据订阅管理
"""

import logging
import asyncio
import uvicorn
from fastapi import FastAPI, HTTPException, Depends, WebSocket, WebSocketDisconnect, Query
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel, Field
from typing import List, Optional, Dict, Any
from datetime import datetime, date, timedelta
import json
import sys
import os

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 创建FastAPI应用
app = FastAPI(
    title="数据服务API",
    description="AI量化交易工具数据服务",
    version="1.0.0"
)

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 数据模型
class StockInfo(BaseModel):
    symbol: str = Field(..., description="股票代码")
    name: str = Field(..., description="股票名称")
    exchange: str = Field(..., description="交易所")
    industry: Optional[str] = Field(None, description="行业")
    market: Optional[str] = Field(None, description="市场")

class QuoteData(BaseModel):
    symbol: str = Field(..., description="股票代码")
    price: float = Field(..., description="当前价格")
    change: float = Field(..., description="涨跌额")
    pct_change: float = Field(..., description="涨跌幅")
    volume: int = Field(..., description="成交量")
    timestamp: str = Field(..., description="时间戳")

class KLineData(BaseModel):
    symbol: str = Field(..., description="股票代码")
    datetime: str = Field(..., description="时间")
    open: float = Field(..., description="开盘价")
    high: float = Field(..., description="最高价")
    low: float = Field(..., description="最低价")
    close: float = Field(..., description="收盘价")
    volume: int = Field(..., description="成交量")
    amount: Optional[float] = Field(None, description="成交额")

# 全局变量
websocket_connections: Dict[str, WebSocket] = {}
subscriptions: Dict[str, Dict[str, Any]] = {}

@app.on_event("startup")
async def startup_event():
    """服务启动事件"""
    try:
        logger.info("数据服务启动成功")
    except Exception as e:
        logger.error(f"数据服务启动失败: {e}")
        raise

@app.on_event("shutdown")
async def shutdown_event():
    """服务关闭事件"""
    try:
        logger.info("数据服务关闭")
    except Exception as e:
        logger.error(f"数据服务关闭异常: {e}")

@app.get("/health")
async def health_check():
    """健康检查接口"""
    return {"status": "healthy", "service": "data-service"}

@app.get("/api/v1/data/stocks", response_model=List[StockInfo])
async def get_stock_list(
    exchange: Optional[str] = Query(None, description="交易所筛选"),
    market: Optional[str] = Query(None, description="市场筛选"),
    limit: int = Query(100, description="返回数量限制")
):
    """获取股票列表"""
    try:
        # 模拟股票数据
        stocks = [
            StockInfo(symbol="000001.SZ", name="平安银行", exchange="深交所", industry="银行", market="主板"),
            StockInfo(symbol="000002.SZ", name="万科A", exchange="深交所", industry="房地产", market="主板"),
            StockInfo(symbol="600000.SH", name="浦发银行", exchange="上交所", industry="银行", market="主板"),
            StockInfo(symbol="600036.SH", name="招商银行", exchange="上交所", industry="银行", market="主板"),
            StockInfo(symbol="000858.SZ", name="五粮液", exchange="深交所", industry="白酒", market="主板"),
        ]

        # 应用筛选条件
        if exchange:
            stocks = [s for s in stocks if exchange.lower() in s.exchange.lower()]

        if market:
            stocks = [s for s in stocks if market.lower() in s.market.lower()]

        # 限制返回数量
        stocks = stocks[:limit]

        return stocks

    except Exception as e:
        logger.error(f"获取股票列表失败: {e}")
        raise HTTPException(status_code=500, detail="获取股票列表失败")

@app.get("/api/v1/data/quote", response_model=List[QuoteData])
async def get_realtime_quotes(
    symbols: str = Query(..., description="股票代码，多个用逗号分隔"),
    source: Optional[str] = Query(None, description="数据源")
):
    """获取实时行情数据"""
    try:
        # 解析股票代码
        symbol_list = [s.strip() for s in symbols.split(',')]

        # 模拟实时行情数据
        import random
        quotes = []
        for symbol in symbol_list:
            base_price = 10 + random.random() * 90
            change = random.uniform(-2, 2)
            quote = QuoteData(
                symbol=symbol,
                price=round(base_price, 2),
                change=round(change, 2),
                pct_change=round((change / base_price) * 100, 2),
                volume=random.randint(100000, 5000000),
                timestamp=datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            )
            quotes.append(quote)

        return quotes

    except Exception as e:
        logger.error(f"获取实时行情失败: {e}")
        raise HTTPException(status_code=500, detail="获取实时行情失败")

@app.get("/api/v1/data/kline", response_model=List[KLineData])
async def get_kline_data(
    symbol: str = Query(..., description="股票代码"),
    start_date: date = Query(..., description="开始日期"),
    end_date: date = Query(..., description="结束日期"),
    period: str = Query("daily", description="周期：daily/weekly/monthly"),
    source: Optional[str] = Query(None, description="数据源")
):
    """获取K线数据"""
    try:
        # 检查日期范围
        if start_date > end_date:
            raise HTTPException(status_code=400, detail="开始日期不能大于结束日期")

        if (end_date - start_date).days > 365:
            raise HTTPException(status_code=400, detail="日期范围不能超过365天")

        # 模拟K线数据
        import random
        import pandas as pd

        dates = pd.date_range(start=start_date, end=end_date, freq='D')
        dates = dates[dates.weekday < 5]  # 只保留工作日

        klines = []
        base_price = 10 + random.random() * 90

        for i, trade_date in enumerate(dates):
            if i > 0:
                # 价格随机游走
                change = random.uniform(-0.05, 0.05)
                base_price = base_price * (1 + change)

            open_price = base_price * (1 + random.uniform(-0.02, 0.02))
            close_price = base_price * (1 + random.uniform(-0.02, 0.02))
            high_price = max(open_price, close_price) * (1 + random.uniform(0, 0.03))
            low_price = min(open_price, close_price) * (1 - random.uniform(0, 0.03))

            kline = KLineData(
                symbol=symbol,
                datetime=trade_date.strftime('%Y-%m-%d'),
                open=round(open_price, 2),
                high=round(high_price, 2),
                low=round(low_price, 2),
                close=round(close_price, 2),
                volume=random.randint(1000000, 10000000),
                amount=round(close_price * random.randint(1000000, 10000000) / 100, 2)
            )
            klines.append(kline)

        return klines

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取K线数据失败: {e}")
        raise HTTPException(status_code=500, detail="获取K线数据失败")

@app.websocket("/ws/data")
async def websocket_endpoint(websocket: WebSocket):
    """WebSocket实时数据推送"""
    await websocket.accept()
    client_id = f"client_{len(websocket_connections)}"
    websocket_connections[client_id] = websocket

    logger.info(f"WebSocket客户端连接: {client_id}")

    try:
        while True:
            # 接收客户端消息
            data = await websocket.receive_text()
            message = json.loads(data)

            if message.get("type") == "subscribe":
                # 处理订阅请求
                symbols = message.get("symbols", [])
                data_type = message.get("data_type", "quote")
                interval = message.get("interval", 60)

                subscriptions[client_id] = {
                    "symbols": symbols,
                    "data_type": data_type,
                    "interval": interval,
                    "last_push": datetime.now()
                }

                await websocket.send_text(json.dumps({
                    "type": "subscribe_ack",
                    "message": f"订阅成功: {len(symbols)} 个股票",
                    "symbols": symbols
                }))

                logger.info(f"客户端 {client_id} 订阅: {symbols}")

            elif message.get("type") == "unsubscribe":
                # 取消订阅
                if client_id in subscriptions:
                    del subscriptions[client_id]

                await websocket.send_text(json.dumps({
                    "type": "unsubscribe_ack",
                    "message": "取消订阅成功"
                }))

                logger.info(f"客户端 {client_id} 取消订阅")

    except WebSocketDisconnect:
        logger.info(f"WebSocket客户端断开连接: {client_id}")
    except Exception as e:
        logger.error(f"WebSocket处理异常: {e}")
    finally:
        # 清理连接和订阅
        if client_id in websocket_connections:
            del websocket_connections[client_id]
        if client_id in subscriptions:
            del subscriptions[client_id]

@app.get("/api/v1/data/stats")
async def get_service_stats():
    """获取服务统计信息"""
    try:
        return {
            "service": "data-service",
            "status": "running",
            "websocket_connections": len(websocket_connections),
            "active_subscriptions": len(subscriptions),
            "timestamp": datetime.now().isoformat()
        }

    except Exception as e:
        logger.error(f"获取服务统计失败: {e}")
        raise HTTPException(status_code=500, detail="获取服务统计失败")

if __name__ == "__main__":
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8002,
        reload=True,
        log_level="info"
    )