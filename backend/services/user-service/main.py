"""
用户服务

提供用户管理相关的API接口，包括：
- 用户注册和登录
- 用户信息管理
- 权限验证
- JWT令牌管理
"""

import logging
import uvicorn
from fastapi import FastAPI, HTTPException, Depends, status
from fastapi.security import HTT<PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from typing import Optional, Dict, Any
import sys
import os

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 创建FastAPI应用
app = FastAPI(
    title="用户服务",
    description="AI量化交易工具用户管理服务",
    version="1.0.0"
)

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# JWT认证
security = HTTPBearer()

# 数据模型
class UserRegister(BaseModel):
    username: str
    email: str
    password: str
    full_name: Optional[str] = None

class UserLogin(BaseModel):
    username: str
    password: str

class UserResponse(BaseModel):
    id: str
    username: str
    email: str
    full_name: Optional[str]
    is_active: bool
    created_at: str

class TokenResponse(BaseModel):
    access_token: str
    token_type: str
    expires_in: int

# 模拟用户数据库
users_db = {}
tokens_db = {}

@app.on_event("startup")
async def startup_event():
    """服务启动事件"""
    try:
        logger.info("用户服务启动成功")
    except Exception as e:
        logger.error(f"用户服务启动失败: {e}")
        raise

@app.on_event("shutdown")
async def shutdown_event():
    """服务关闭事件"""
    try:
        logger.info("用户服务关闭")
    except Exception as e:
        logger.error(f"用户服务关闭异常: {e}")

@app.get("/health")
async def health_check():
    """健康检查接口"""
    return {"status": "healthy", "service": "user-service"}

@app.post("/api/v1/users/register", response_model=UserResponse)
async def register_user(user_data: UserRegister):
    """用户注册"""
    try:
        # 检查用户是否已存在
        if user_data.username in users_db:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="用户名已存在"
            )

        # 创建用户
        user_id = f"user_{len(users_db) + 1}"
        user = {
            "id": user_id,
            "username": user_data.username,
            "email": user_data.email,
            "password_hash": f"hashed_{user_data.password}",  # 实际应用中需要真正的哈希
            "full_name": user_data.full_name,
            "is_active": True,
            "created_at": "2024-01-01T00:00:00Z"
        }

        users_db[user_data.username] = user
        logger.info(f"用户注册成功: {user_data.username}")

        return UserResponse(**user)

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"用户注册失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="用户注册失败"
        )

@app.post("/api/v1/users/login", response_model=TokenResponse)
async def login_user(login_data: UserLogin):
    """用户登录"""
    try:
        # 验证用户凭据
        user = users_db.get(login_data.username)
        if not user or user["password_hash"] != f"hashed_{login_data.password}":
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="用户名或密码错误"
            )

        # 生成访问令牌
        access_token = f"token_{user['id']}_{len(tokens_db)}"
        tokens_db[access_token] = {
            "user_id": user["id"],
            "username": user["username"],
            "expires_at": "2024-12-31T23:59:59Z"
        }

        logger.info(f"用户登录成功: {login_data.username}")

        return TokenResponse(
            access_token=access_token,
            token_type="bearer",
            expires_in=3600
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"用户登录失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="用户登录失败"
        )

async def get_current_user(credentials: HTTPAuthorizationCredentials = Depends(security)):
    """获取当前用户"""
    token = credentials.credentials
    token_data = tokens_db.get(token)

    if not token_data:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="无效的访问令牌"
        )

    user = users_db.get(token_data["username"])
    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="用户不存在"
        )

    return UserResponse(**user)

@app.get("/api/v1/users/me", response_model=UserResponse)
async def get_user_profile(current_user: UserResponse = Depends(get_current_user)):
    """获取用户信息"""
    return current_user

if __name__ == "__main__":
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8001,
        reload=True,
        log_level="info"
    )