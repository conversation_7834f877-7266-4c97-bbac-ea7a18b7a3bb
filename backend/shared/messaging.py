"""
消息队列管理模块

提供Kafka消息队列的统一接口，支持：
- 消息发布和订阅
- 主题管理
- 消费者组管理
- 消息序列化和反序列化
"""

import json
import logging
from typing import Dict, Any, Optional, Callable, List
from datetime import datetime

logger = logging.getLogger(__name__)

try:
    from kafka import KafkaProducer, KafkaConsumer
    from kafka.errors import KafkaError
    KAFKA_AVAILABLE = True
except ImportError:
    KafkaProducer = None
    KafkaConsumer = None
    KafkaError = Exception
    KAFKA_AVAILABLE = False


class MessageQueue:
    """消息队列管理器"""

    def __init__(self, bootstrap_servers: str = "localhost:9092"):
        self.bootstrap_servers = bootstrap_servers
        self._producer = None
        self._consumers = {}
        self._initialized = False

    def initialize(self):
        """初始化消息队列连接"""
        if not KAFKA_AVAILABLE:
            logger.warning("Kafka不可用，使用模拟模式")
            self._initialized = True
            return

        try:
            # 初始化生产者
            self._producer = KafkaProducer(
                bootstrap_servers=self.bootstrap_servers,
                value_serializer=lambda v: json.dumps(v, ensure_ascii=False, default=str).encode('utf-8'),
                key_serializer=lambda k: k.encode('utf-8') if k else None,
                acks='all',
                retries=3,
                batch_size=16384,
                linger_ms=10,
                buffer_memory=33554432
            )

            self._initialized = True
            logger.info("消息队列初始化成功")

        except Exception as e:
            logger.error(f"消息队列初始化失败: {e}")
            raise

    def publish(self, topic: str, message: Dict[str, Any], key: Optional[str] = None):
        """发布消息到指定主题"""
        if not self._initialized:
            self.initialize()

        if not KAFKA_AVAILABLE:
            logger.info(f"模拟发送消息到主题 {topic}: {message}")
            return

        try:
            # 添加时间戳
            message['timestamp'] = datetime.now().isoformat()

            future = self._producer.send(topic, value=message, key=key)
            record_metadata = future.get(timeout=10)

            logger.debug(f"消息发送成功 - 主题: {topic}, 分区: {record_metadata.partition}, 偏移量: {record_metadata.offset}")

        except KafkaError as e:
            logger.error(f"消息发送失败 - 主题: {topic}, 错误: {e}")
            raise
        except Exception as e:
            logger.error(f"消息发送异常 - 主题: {topic}, 错误: {e}")
            raise

    def subscribe(self, topic: str, group_id: str, handler: Callable[[Dict[str, Any]], None]):
        """订阅主题消息"""
        if not self._initialized:
            self.initialize()

        if not KAFKA_AVAILABLE:
            logger.info(f"模拟订阅主题 {topic}，消费者组: {group_id}")
            return

        try:
            consumer = KafkaConsumer(
                topic,
                bootstrap_servers=self.bootstrap_servers,
                group_id=group_id,
                value_deserializer=lambda m: json.loads(m.decode('utf-8')),
                key_deserializer=lambda k: k.decode('utf-8') if k else None,
                auto_offset_reset='latest',
                enable_auto_commit=True,
                auto_commit_interval_ms=1000,
                session_timeout_ms=30000,
                heartbeat_interval_ms=3000
            )

            self._consumers[f"{topic}_{group_id}"] = consumer

            logger.info(f"开始消费主题 {topic}，消费者组: {group_id}")

            # 消费消息
            for message in consumer:
                try:
                    handler(message.value)
                except Exception as e:
                    logger.error(f"消息处理失败 - 主题: {topic}, 错误: {e}")

        except Exception as e:
            logger.error(f"订阅主题失败 - 主题: {topic}, 错误: {e}")
            raise

    def close(self):
        """关闭消息队列连接"""
        try:
            if self._producer:
                self._producer.close()
                logger.info("生产者连接已关闭")

            for consumer_key, consumer in self._consumers.items():
                consumer.close()
                logger.info(f"消费者连接已关闭: {consumer_key}")

            self._consumers.clear()
            self._initialized = False

        except Exception as e:
            logger.error(f"关闭消息队列连接时出错: {e}")


# 预定义的主题常量
class Topics:
    """消息主题常量"""
    MARKET_DATA = "market_data"
    TRADING_SIGNALS = "trading_signals"
    RISK_ALERTS = "risk_alerts"
    SYSTEM_LOGS = "system_logs"
    USER_EVENTS = "user_events"
    STRATEGY_EVENTS = "strategy_events"
    ORDER_EVENTS = "order_events"
    BACKTEST_EVENTS = "backtest_events"


# 全局消息队列实例
message_queue = MessageQueue()


def publish_market_data(symbol: str, data: Dict[str, Any]):
    """发布市场数据"""
    message = {
        "type": "market_data",
        "symbol": symbol,
        "data": data
    }
    message_queue.publish(Topics.MARKET_DATA, message, key=symbol)


def publish_trading_signal(strategy_id: str, signal: Dict[str, Any]):
    """发布交易信号"""
    message = {
        "type": "trading_signal",
        "strategy_id": strategy_id,
        "signal": signal
    }
    message_queue.publish(Topics.TRADING_SIGNALS, message, key=strategy_id)


def publish_risk_alert(user_id: str, alert: Dict[str, Any]):
    """发布风险警报"""
    message = {
        "type": "risk_alert",
        "user_id": user_id,
        "alert": alert
    }
    message_queue.publish(Topics.RISK_ALERTS, message, key=user_id)


def publish_system_log(level: str, message: str, context: Optional[Dict[str, Any]] = None):
    """发布系统日志"""
    log_message = {
        "type": "system_log",
        "level": level,
        "message": message,
        "context": context or {}
    }
    message_queue.publish(Topics.SYSTEM_LOGS, log_message)