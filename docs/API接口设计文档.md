# AI量化交易工具API接口设计文档

## 1. API设计概述

### 1.1 设计原则
- **RESTful风格**：遵循REST架构风格，使用标准HTTP方法
- **统一响应格式**：所有接口使用统一的响应数据格式
- **版本控制**：支持API版本管理，向后兼容
- **安全认证**：使用JWT Token进行身份认证
- **错误处理**：标准化的错误码和错误信息
- **文档完善**：自动生成API文档，提供详细示例

### 1.2 基础信息
- **Base URL**：`https://api.quantitative-tools.com`
- **API版本**：`v1`
- **完整URL**：`https://api.quantitative-tools.com/api/v1`
- **认证方式**：Bearer Token (JWT)
- **数据格式**：JSON
- **字符编码**：UTF-8

### 1.3 HTTP状态码规范
| 状态码 | 含义 | 使用场景 |
|--------|------|----------|
| 200 | OK | 请求成功 |
| 201 | Created | 资源创建成功 |
| 204 | No Content | 删除成功，无返回内容 |
| 400 | Bad Request | 请求参数错误 |
| 401 | Unauthorized | 未认证或认证失败 |
| 403 | Forbidden | 无权限访问 |
| 404 | Not Found | 资源不存在 |
| 409 | Conflict | 资源冲突 |
| 422 | Unprocessable Entity | 请求格式正确但语义错误 |
| 429 | Too Many Requests | 请求频率超限 |
| 500 | Internal Server Error | 服务器内部错误 |

## 2. 统一响应格式

### 2.1 成功响应格式
```json
{
    "code": 200,
    "message": "success",
    "data": {
        // 具体业务数据
    },
    "timestamp": "2024-01-01T00:00:00Z",
    "request_id": "550e8400-e29b-41d4-a716-446655440000"
}
```

### 2.2 错误响应格式
```json
{
    "code": 400,
    "message": "参数验证失败",
    "error": {
        "error_code": "INVALID_PARAMETER",
        "error_message": "symbol参数不能为空",
        "details": {
            "field": "symbol",
            "value": "",
            "constraint": "required"
        }
    },
    "timestamp": "2024-01-01T00:00:00Z",
    "request_id": "550e8400-e29b-41d4-a716-446655440000"
}
```

### 2.3 分页响应格式
```json
{
    "code": 200,
    "message": "success",
    "data": {
        "items": [
            // 数据列表
        ],
        "pagination": {
            "page": 1,
            "page_size": 20,
            "total": 100,
            "total_pages": 5,
            "has_next": true,
            "has_prev": false
        }
    },
    "timestamp": "2024-01-01T00:00:00Z",
    "request_id": "550e8400-e29b-41d4-a716-446655440000"
}
```

## 3. 认证和授权

### 3.1 用户认证接口

#### 3.1.1 用户登录
```http
POST /api/v1/auth/login
Content-Type: application/json

{
    "username": "<EMAIL>",
    "password": "password123"
}
```

**响应示例**：
```json
{
    "code": 200,
    "message": "登录成功",
    "data": {
        "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
        "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
        "token_type": "Bearer",
        "expires_in": 3600,
        "user": {
            "id": 1,
            "username": "<EMAIL>",
            "full_name": "张三",
            "roles": ["trader", "analyst"]
        }
    }
}
```

#### 3.1.2 刷新Token
```http
POST /api/v1/auth/refresh
Content-Type: application/json
Authorization: Bearer <refresh_token>

{
    "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
}
```

#### 3.1.3 用户注销
```http
POST /api/v1/auth/logout
Authorization: Bearer <access_token>
```

### 3.2 权限验证
所有需要认证的接口都需要在请求头中包含JWT Token：
```http
Authorization: Bearer <access_token>
```

## 4. 数据服务接口

### 4.1 行情数据接口

#### 4.1.1 获取实时行情
```http
GET /api/v1/market/realtime?symbols=000001.SZ,000002.SZ
Authorization: Bearer <access_token>
```

**响应示例**：
```json
{
    "code": 200,
    "message": "success",
    "data": {
        "000001.SZ": {
            "symbol": "000001.SZ",
            "name": "平安银行",
            "price": 10.50,
            "change": 0.15,
            "change_percent": 1.45,
            "volume": 1000000,
            "amount": 10500000.00,
            "high": 10.60,
            "low": 10.30,
            "open": 10.35,
            "prev_close": 10.35,
            "timestamp": "2024-01-01T09:30:00Z"
        }
    }
}
```

#### 4.1.2 获取历史行情
```http
GET /api/v1/market/history?symbol=000001.SZ&start_date=2024-01-01&end_date=2024-01-31&frequency=daily
Authorization: Bearer <access_token>
```

**查询参数**：
- `symbol`: 股票代码（必填）
- `start_date`: 开始日期，格式YYYY-MM-DD（必填）
- `end_date`: 结束日期，格式YYYY-MM-DD（必填）
- `frequency`: 数据频率，可选值：daily, hourly, minute（默认daily）
- `adjust`: 复权类型，可选值：none, forward, backward（默认none）

#### 4.1.3 获取K线数据
```http
GET /api/v1/market/kline?symbol=000001.SZ&interval=1d&limit=100
Authorization: Bearer <access_token>
```

### 4.2 基础数据接口

#### 4.2.1 获取股票列表
```http
GET /api/v1/market/stocks?market=SZ&status=active&page=1&page_size=20
Authorization: Bearer <access_token>
```

#### 4.2.2 获取股票基本信息
```http
GET /api/v1/market/stocks/000001.SZ
Authorization: Bearer <access_token>
```

## 5. 策略服务接口

### 5.1 策略管理接口

#### 5.1.1 创建策略
```http
POST /api/v1/strategies
Content-Type: application/json
Authorization: Bearer <access_token>

{
    "name": "双均线策略",
    "description": "基于5日和20日均线的交易策略",
    "code": "# 策略代码\ndef initialize(context):\n    pass\n\ndef handle_data(context, data):\n    pass",
    "parameters": {
        "short_window": 5,
        "long_window": 20,
        "symbols": ["000001.SZ", "000002.SZ"]
    },
    "risk_rules": {
        "max_position_ratio": 0.3,
        "stop_loss_ratio": 0.05
    }
}
```

#### 5.1.2 获取策略列表
```http
GET /api/v1/strategies?status=active&page=1&page_size=20
Authorization: Bearer <access_token>
```

#### 5.1.3 获取策略详情
```http
GET /api/v1/strategies/123
Authorization: Bearer <access_token>
```

#### 5.1.4 更新策略
```http
PUT /api/v1/strategies/123
Content-Type: application/json
Authorization: Bearer <access_token>

{
    "name": "双均线策略v2",
    "parameters": {
        "short_window": 10,
        "long_window": 30
    }
}
```

#### 5.1.5 删除策略
```http
DELETE /api/v1/strategies/123
Authorization: Bearer <access_token>
```

### 5.2 策略运行接口

#### 5.2.1 启动策略
```http
POST /api/v1/strategies/123/start
Content-Type: application/json
Authorization: Bearer <access_token>

{
    "mode": "live",  // live: 实盘, paper: 模拟
    "initial_capital": 100000.00
}
```

#### 5.2.2 停止策略
```http
POST /api/v1/strategies/123/stop
Authorization: Bearer <access_token>
```

#### 5.2.3 获取策略运行状态
```http
GET /api/v1/strategies/123/status
Authorization: Bearer <access_token>
```

#### 5.2.4 获取策略信号
```http
GET /api/v1/strategies/123/signals?start_time=2024-01-01T00:00:00Z&end_time=2024-01-31T23:59:59Z
Authorization: Bearer <access_token>
```

## 6. 交易服务接口

### 6.1 订单管理接口

#### 6.1.1 创建订单
```http
POST /api/v1/orders
Content-Type: application/json
Authorization: Bearer <access_token>

{
    "symbol": "000001.SZ",
    "side": "buy",  // buy: 买入, sell: 卖出
    "order_type": "limit",  // market: 市价, limit: 限价, stop: 止损
    "quantity": 1000,
    "price": 10.50,
    "strategy_id": 123,
    "time_in_force": "GTC"  // GTC: 撤销前有效, IOC: 立即成交或撤销, FOK: 全部成交或撤销
}
```

#### 6.1.2 获取订单列表
```http
GET /api/v1/orders?status=active&symbol=000001.SZ&page=1&page_size=20
Authorization: Bearer <access_token>
```

#### 6.1.3 获取订单详情
```http
GET /api/v1/orders/456
Authorization: Bearer <access_token>
```

#### 6.1.4 撤销订单
```http
DELETE /api/v1/orders/456
Authorization: Bearer <access_token>
```

### 6.2 持仓管理接口

#### 6.2.1 获取持仓列表
```http
GET /api/v1/positions
Authorization: Bearer <access_token>
```

#### 6.2.2 获取单个持仓
```http
GET /api/v1/positions/000001.SZ
Authorization: Bearer <access_token>
```

### 6.3 成交记录接口

#### 6.3.1 获取成交记录
```http
GET /api/v1/trades?start_date=2024-01-01&end_date=2024-01-31&page=1&page_size=20
Authorization: Bearer <access_token>
```

## 7. 回测服务接口

### 7.1 回测任务接口

#### 7.1.1 创建回测任务
```http
POST /api/v1/backtests
Content-Type: application/json
Authorization: Bearer <access_token>

{
    "strategy_id": 123,
    "start_date": "2023-01-01",
    "end_date": "2023-12-31",
    "initial_capital": 1000000.00,
    "benchmark": "000300.SH",
    "parameters": {
        "commission_rate": 0.0003,
        "slippage": 0.001
    }
}
```

#### 7.1.2 获取回测任务列表
```http
GET /api/v1/backtests?strategy_id=123&status=completed&page=1&page_size=20
Authorization: Bearer <access_token>
```

#### 7.1.3 获取回测结果
```http
GET /api/v1/backtests/789/result
Authorization: Bearer <access_token>
```

**响应示例**：
```json
{
    "code": 200,
    "message": "success",
    "data": {
        "backtest_id": 789,
        "strategy_id": 123,
        "period": {
            "start_date": "2023-01-01",
            "end_date": "2023-12-31"
        },
        "performance": {
            "total_return": 0.1523,
            "annual_return": 0.1523,
            "sharpe_ratio": 1.25,
            "max_drawdown": 0.0856,
            "volatility": 0.1234,
            "win_rate": 0.6234,
            "profit_loss_ratio": 1.45
        },
        "trades_summary": {
            "total_trades": 156,
            "winning_trades": 97,
            "losing_trades": 59,
            "avg_trade_return": 0.0098
        },
        "benchmark_comparison": {
            "benchmark_return": 0.0856,
            "alpha": 0.0667,
            "beta": 0.89,
            "information_ratio": 0.78
        }
    }
}
```

## 8. 风险管理接口

### 8.1 风险规则接口

#### 8.1.1 创建风险规则
```http
POST /api/v1/risk/rules
Content-Type: application/json
Authorization: Bearer <access_token>

{
    "name": "单股持仓限制",
    "rule_type": "position_limit",
    "conditions": {
        "max_position_ratio": 0.1,
        "symbols": ["000001.SZ", "000002.SZ"]
    },
    "actions": {
        "alert": true,
        "auto_reduce": true,
        "notification": ["email", "sms"]
    }
}
```

#### 8.1.2 获取风险规则列表
```http
GET /api/v1/risk/rules?status=active
Authorization: Bearer <access_token>
```

### 8.2 风险监控接口

#### 8.2.1 获取实时风险指标
```http
GET /api/v1/risk/metrics
Authorization: Bearer <access_token>
```

#### 8.2.2 获取风险事件
```http
GET /api/v1/risk/events?level=high&start_time=2024-01-01T00:00:00Z
Authorization: Bearer <access_token>
```

## 9. WebSocket实时接口

### 9.1 连接建立
```javascript
// WebSocket连接地址
const ws = new WebSocket('wss://api.quantitative-tools.com/ws/v1');

// 认证消息
const authMessage = {
    type: 'auth',
    token: 'Bearer <access_token>'
};
ws.send(JSON.stringify(authMessage));
```

### 9.2 消息格式规范

#### 9.2.1 客户端请求格式
```json
{
    "id": "unique_request_id",
    "type": "subscribe|unsubscribe|request",
    "channel": "market_data|order_updates|strategy_signals",
    "params": {
        // 具体参数
    }
}
```

#### 9.2.2 服务端响应格式
```json
{
    "id": "unique_request_id",
    "type": "response|push|error",
    "channel": "market_data|order_updates|strategy_signals",
    "data": {
        // 具体数据
    },
    "timestamp": "2024-01-01T09:30:00Z"
}
```

### 9.3 实时行情订阅

#### 9.3.1 订阅行情数据
```json
{
    "id": "req_001",
    "type": "subscribe",
    "channel": "market_data",
    "params": {
        "symbols": ["000001.SZ", "000002.SZ"],
        "fields": ["price", "volume", "change"]
    }
}
```

#### 9.3.2 行情数据推送
```json
{
    "type": "push",
    "channel": "market_data",
    "data": {
        "symbol": "000001.SZ",
        "price": 10.52,
        "volume": 1500000,
        "change": 0.17,
        "change_percent": 1.64,
        "timestamp": "2024-01-01T09:30:15Z"
    }
}
```

### 9.4 订单状态推送

#### 9.4.1 订阅订单更新
```json
{
    "id": "req_002",
    "type": "subscribe",
    "channel": "order_updates",
    "params": {
        "user_id": 123
    }
}
```

#### 9.4.2 订单状态推送
```json
{
    "type": "push",
    "channel": "order_updates",
    "data": {
        "order_id": 456,
        "symbol": "000001.SZ",
        "status": "filled",
        "filled_quantity": 1000,
        "avg_price": 10.51,
        "timestamp": "2024-01-01T09:30:20Z"
    }
}
```

### 9.5 策略信号推送

#### 9.5.1 订阅策略信号
```json
{
    "id": "req_003",
    "type": "subscribe",
    "channel": "strategy_signals",
    "params": {
        "strategy_id": 123
    }
}
```

#### 9.5.2 策略信号推送
```json
{
    "type": "push",
    "channel": "strategy_signals",
    "data": {
        "strategy_id": 123,
        "symbol": "000001.SZ",
        "signal_type": "buy",
        "signal_strength": 0.85,
        "price": 10.50,
        "quantity": 1000,
        "timestamp": "2024-01-01T09:30:25Z"
    }
}
```

## 10. 错误码定义

### 10.1 通用错误码
| 错误码 | HTTP状态码 | 错误信息 | 说明 |
|--------|-----------|----------|------|
| SUCCESS | 200 | 成功 | 请求处理成功 |
| INVALID_PARAMETER | 400 | 参数错误 | 请求参数不合法 |
| UNAUTHORIZED | 401 | 未认证 | 缺少认证信息或认证失败 |
| FORBIDDEN | 403 | 无权限 | 没有访问权限 |
| NOT_FOUND | 404 | 资源不存在 | 请求的资源不存在 |
| METHOD_NOT_ALLOWED | 405 | 方法不允许 | HTTP方法不被支持 |
| CONFLICT | 409 | 资源冲突 | 资源状态冲突 |
| RATE_LIMIT_EXCEEDED | 429 | 请求频率超限 | 超过API调用频率限制 |
| INTERNAL_ERROR | 500 | 服务器内部错误 | 服务器处理异常 |

### 10.2 业务错误码
| 错误码 | HTTP状态码 | 错误信息 | 说明 |
|--------|-----------|----------|------|
| STRATEGY_NOT_FOUND | 404 | 策略不存在 | 指定的策略ID不存在 |
| STRATEGY_ALREADY_RUNNING | 409 | 策略已在运行 | 策略当前状态不允许启动 |
| INSUFFICIENT_BALANCE | 400 | 余额不足 | 账户余额不足以执行交易 |
| INVALID_ORDER_PARAMS | 400 | 订单参数错误 | 订单参数不符合要求 |
| ORDER_NOT_FOUND | 404 | 订单不存在 | 指定的订单ID不存在 |
| POSITION_NOT_FOUND | 404 | 持仓不存在 | 指定的持仓不存在 |
| RISK_RULE_VIOLATION | 400 | 违反风控规则 | 操作违反了设定的风控规则 |
| MARKET_CLOSED | 400 | 市场未开放 | 当前时间市场未开放 |
| SYMBOL_NOT_SUPPORTED | 400 | 不支持的股票代码 | 股票代码不在支持范围内 |

## 11. API限流规则

### 11.1 限流策略
- **用户级限流**：每个用户每分钟最多1000次请求
- **IP级限流**：每个IP每分钟最多5000次请求
- **接口级限流**：特定接口有独立的限流规则
- **WebSocket限流**：每个连接每秒最多100条消息

### 11.2 限流响应头
```http
X-RateLimit-Limit: 1000
X-RateLimit-Remaining: 999
X-RateLimit-Reset: 1640995200
```

### 11.3 超限响应
```json
{
    "code": 429,
    "message": "请求频率超限",
    "error": {
        "error_code": "RATE_LIMIT_EXCEEDED",
        "error_message": "每分钟最多允许1000次请求",
        "retry_after": 60
    }
}
```

## 12. API版本管理

### 12.1 版本策略
- **URL版本控制**：通过URL路径指定版本，如`/api/v1/`、`/api/v2/`
- **向后兼容**：新版本保持对旧版本的兼容性
- **废弃通知**：提前通知API废弃计划
- **平滑迁移**：提供迁移指南和工具

### 12.2 版本生命周期
- **开发版本**：内部开发和测试使用
- **测试版本**：公开测试，收集反馈
- **稳定版本**：生产环境使用
- **废弃版本**：计划废弃，建议迁移

### 12.3 版本兼容性
```http
# 指定版本
GET /api/v1/strategies

# 使用Accept头指定版本
GET /api/strategies
Accept: application/vnd.quantitative-tools.v1+json
```

## 13. API安全规范

### 13.1 HTTPS强制
- 所有API接口强制使用HTTPS
- 禁用不安全的SSL/TLS版本
- 使用强加密算法套件

### 13.2 输入验证
- 严格验证所有输入参数
- 防止SQL注入攻击
- 防止XSS攻击
- 参数长度和格式限制

### 13.3 敏感数据保护
- 敏感数据不在URL中传输
- 响应中不包含敏感信息
- 日志中脱敏处理
- 数据传输加密

## 14. API监控和日志

### 14.1 监控指标
- **请求量**：每秒请求数(QPS)
- **响应时间**：平均响应时间和P99响应时间
- **错误率**：4xx和5xx错误比例
- **可用性**：服务可用性百分比

### 14.2 日志规范
```json
{
    "timestamp": "2024-01-01T09:30:00Z",
    "request_id": "550e8400-e29b-41d4-a716-446655440000",
    "method": "GET",
    "path": "/api/v1/strategies",
    "status_code": 200,
    "response_time": 150,
    "user_id": 123,
    "ip_address": "*************",
    "user_agent": "QuantitativeTools/1.0"
}
```

### 14.3 告警规则
- 错误率超过5%时告警
- 响应时间超过1秒时告警
- 可用性低于99.9%时告警
- 异常请求模式检测
