#!/bin/bash

# AI量化交易工具系统启动脚本
# 用于快速启动开发环境和生产环境

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_debug() {
    echo -e "${BLUE}[DEBUG]${NC} $1"
}

# 检查依赖
check_dependencies() {
    log_info "检查系统依赖..."
    
    # 检查Python版本
    if ! command -v python3 &> /dev/null; then
        log_error "Python3 未安装，请先安装Python 3.9或更高版本"
        exit 1
    fi
    
    python_version=$(python3 --version | cut -d' ' -f2 | cut -d'.' -f1,2)
    required_version="3.9"
    
    if [ "$(printf '%s\n' "$required_version" "$python_version" | sort -V | head -n1)" != "$required_version" ]; then
        log_error "Python版本过低，需要3.9或更高版本，当前版本: $python_version"
        exit 1
    fi
    
    log_info "Python版本检查通过: $python_version"
    
    # 检查Docker（可选）
    if command -v docker &> /dev/null; then
        log_info "Docker已安装: $(docker --version)"
    else
        log_warn "Docker未安装，将使用本地环境运行"
    fi
    
    # 检查Docker Compose（可选）
    if command -v docker-compose &> /dev/null; then
        log_info "Docker Compose已安装: $(docker-compose --version)"
    else
        log_warn "Docker Compose未安装"
    fi
}

# 设置环境
setup_environment() {
    log_info "设置运行环境..."
    
    # 创建必要的目录
    mkdir -p logs
    mkdir -p uploads
    mkdir -p data
    mkdir -p temp
    
    # 检查.env文件
    if [ ! -f .env ]; then
        log_warn ".env文件不存在，从模板创建..."
        cp .env.example .env
        log_info "请编辑.env文件配置相关参数"
    fi
    
    # 创建虚拟环境（如果不存在）
    if [ ! -d "venv" ]; then
        log_info "创建Python虚拟环境..."
        python3 -m venv venv
    fi
    
    # 激活虚拟环境
    source venv/bin/activate
    
    # 升级pip
    log_info "升级pip..."
    pip install --upgrade pip
    
    # 安装依赖
    log_info "安装Python依赖包..."
    pip install -r requirements.txt
    
    log_info "环境设置完成"
}

# 运行测试
run_tests() {
    log_info "运行系统测试..."
    
    source venv/bin/activate
    
    # 运行基础测试
    python -m pytest tests/test_basic.py -v
    
    if [ $? -eq 0 ]; then
        log_info "测试通过"
    else
        log_error "测试失败"
        exit 1
    fi
}

# 启动开发服务器
start_dev_server() {
    log_info "启动开发服务器..."
    
    source venv/bin/activate
    
    # 设置开发环境变量
    export DEBUG=true
    export ENVIRONMENT=development
    
    # 启动服务器
    python main.py
}

# 使用Docker启动
start_with_docker() {
    log_info "使用Docker启动系统..."
    
    # 检查Docker是否运行
    if ! docker info &> /dev/null; then
        log_error "Docker未运行，请先启动Docker"
        exit 1
    fi
    
    # 构建镜像
    log_info "构建Docker镜像..."
    docker build -t ai-quantitative-tools .
    
    # 启动服务
    log_info "启动Docker Compose服务..."
    docker-compose up -d
    
    # 等待服务启动
    log_info "等待服务启动..."
    sleep 10
    
    # 检查服务状态
    docker-compose ps
    
    log_info "系统启动完成"
    log_info "访问地址: http://localhost:8000"
    log_info "API文档: http://localhost:8000/docs"
    log_info "监控面板: http://localhost:3000 (Grafana)"
    log_info "日志查看: docker-compose logs -f app"
}

# 停止服务
stop_services() {
    log_info "停止服务..."
    
    if [ -f docker-compose.yml ]; then
        docker-compose down
        log_info "Docker服务已停止"
    fi
    
    # 停止本地进程
    pkill -f "python main.py" || true
    
    log_info "所有服务已停止"
}

# 清理环境
cleanup() {
    log_info "清理环境..."
    
    # 停止服务
    stop_services
    
    # 清理Docker资源
    if command -v docker &> /dev/null; then
        docker system prune -f
        log_info "Docker资源清理完成"
    fi
    
    # 清理临时文件
    rm -rf temp/*
    rm -rf logs/*.log
    
    log_info "环境清理完成"
}

# 显示帮助信息
show_help() {
    echo "AI量化交易工具系统启动脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  setup     设置开发环境"
    echo "  test      运行系统测试"
    echo "  dev       启动开发服务器"
    echo "  docker    使用Docker启动"
    echo "  stop      停止所有服务"
    echo "  clean     清理环境"
    echo "  help      显示帮助信息"
    echo ""
    echo "示例:"
    echo "  $0 setup    # 首次运行，设置环境"
    echo "  $0 test     # 运行测试"
    echo "  $0 dev      # 启动开发服务器"
    echo "  $0 docker   # 使用Docker启动完整系统"
    echo ""
}

# 主函数
main() {
    case "${1:-help}" in
        setup)
            check_dependencies
            setup_environment
            ;;
        test)
            run_tests
            ;;
        dev)
            check_dependencies
            start_dev_server
            ;;
        docker)
            start_with_docker
            ;;
        stop)
            stop_services
            ;;
        clean)
            cleanup
            ;;
        help|--help|-h)
            show_help
            ;;
        *)
            log_error "未知选项: $1"
            show_help
            exit 1
            ;;
    esac
}

# 脚本入口
main "$@"
