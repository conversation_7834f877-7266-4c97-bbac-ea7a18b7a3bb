-- AI量化交易工具系统 ClickHouse数据库初始化脚本
-- 创建时序数据存储表结构

-- 创建数据库
CREATE DATABASE IF NOT EXISTS market_data;

-- 使用数据库
USE market_data;

-- 创建股票基础信息表
CREATE TABLE IF NOT EXISTS stock_info (
    symbol String,
    name String,
    exchange String,
    sector String,
    industry String,
    market_cap Float64,
    list_date Date,
    created_at DateTime DEFAULT now(),
    updated_at DateTime DEFAULT now()
) ENGINE = ReplacingMergeTree(updated_at)
ORDER BY symbol
SETTINGS index_granularity = 8192;

-- 创建日线行情数据表
CREATE TABLE IF NOT EXISTS daily_quotes (
    symbol String,
    trade_date Date,
    open Float64,
    high Float64,
    low Float64,
    close Float64,
    volume UInt64,
    amount Float64,
    turnover_rate Float64,
    pe_ratio Float64,
    pb_ratio Float64,
    created_at DateTime DEFAULT now()
) ENGINE = ReplacingMergeTree(created_at)
PARTITION BY toYYYYMM(trade_date)
ORDER BY (symbol, trade_date)
SETTINGS index_granularity = 8192;

-- 创建分钟级行情数据表
CREATE TABLE IF NOT EXISTS minute_quotes (
    symbol String,
    datetime DateTime,
    open Float64,
    high Float64,
    low Float64,
    close Float64,
    volume UInt64,
    amount Float64,
    created_at DateTime DEFAULT now()
) ENGINE = ReplacingMergeTree(created_at)
PARTITION BY toYYYYMMDD(datetime)
ORDER BY (symbol, datetime)
SETTINGS index_granularity = 8192;

-- 创建实时tick数据表
CREATE TABLE IF NOT EXISTS tick_data (
    symbol String,
    datetime DateTime64(3),
    price Float64,
    volume UInt32,
    direction Int8, -- 1: 买入, -1: 卖出, 0: 未知
    bid_price Float64,
    ask_price Float64,
    bid_volume UInt32,
    ask_volume UInt32,
    created_at DateTime DEFAULT now()
) ENGINE = MergeTree()
PARTITION BY toYYYYMMDD(datetime)
ORDER BY (symbol, datetime)
SETTINGS index_granularity = 8192;

-- 创建财务数据表
CREATE TABLE IF NOT EXISTS financial_data (
    symbol String,
    report_date Date,
    period String, -- Q1, Q2, Q3, Q4, Y
    revenue Float64,
    net_profit Float64,
    total_assets Float64,
    total_liabilities Float64,
    shareholders_equity Float64,
    operating_cash_flow Float64,
    free_cash_flow Float64,
    roe Float64,
    roa Float64,
    debt_to_equity Float64,
    current_ratio Float64,
    created_at DateTime DEFAULT now(),
    updated_at DateTime DEFAULT now()
) ENGINE = ReplacingMergeTree(updated_at)
PARTITION BY toYYYY(report_date)
ORDER BY (symbol, report_date, period)
SETTINGS index_granularity = 8192;

-- 创建技术指标数据表
CREATE TABLE IF NOT EXISTS technical_indicators (
    symbol String,
    trade_date Date,
    indicator_name String,
    indicator_value Float64,
    timeframe String, -- 1d, 1h, 5m, etc.
    created_at DateTime DEFAULT now()
) ENGINE = ReplacingMergeTree(created_at)
PARTITION BY toYYYYMM(trade_date)
ORDER BY (symbol, trade_date, indicator_name, timeframe)
SETTINGS index_granularity = 8192;

-- 创建策略信号表
CREATE TABLE IF NOT EXISTS strategy_signals (
    signal_id String,
    strategy_id String,
    symbol String,
    signal_type String, -- buy, sell, hold
    signal_strength Float64,
    price Float64,
    quantity UInt32,
    timestamp DateTime,
    metadata String, -- JSON格式的元数据
    created_at DateTime DEFAULT now()
) ENGINE = MergeTree()
PARTITION BY toYYYYMMDD(timestamp)
ORDER BY (strategy_id, symbol, timestamp)
SETTINGS index_granularity = 8192;

-- 创建交易执行记录表
CREATE TABLE IF NOT EXISTS trade_executions (
    execution_id String,
    order_id String,
    symbol String,
    side String, -- buy, sell
    quantity UInt32,
    price Float64,
    commission Float64,
    execution_time DateTime,
    created_at DateTime DEFAULT now()
) ENGINE = MergeTree()
PARTITION BY toYYYYMMDD(execution_time)
ORDER BY (symbol, execution_time)
SETTINGS index_granularity = 8192;

-- 创建风险指标表
CREATE TABLE IF NOT EXISTS risk_metrics (
    metric_id String,
    user_id String,
    metric_type String, -- var, max_drawdown, sharpe_ratio, etc.
    metric_value Float64,
    calculation_date Date,
    timeframe String, -- daily, weekly, monthly
    created_at DateTime DEFAULT now()
) ENGINE = ReplacingMergeTree(created_at)
PARTITION BY toYYYYMM(calculation_date)
ORDER BY (user_id, metric_type, calculation_date, timeframe)
SETTINGS index_granularity = 8192;

-- 创建回测结果表
CREATE TABLE IF NOT EXISTS backtest_results (
    backtest_id String,
    strategy_id String,
    symbol String,
    trade_date Date,
    position Float64,
    market_value Float64,
    cash Float64,
    total_value Float64,
    daily_return Float64,
    cumulative_return Float64,
    created_at DateTime DEFAULT now()
) ENGINE = MergeTree()
PARTITION BY toYYYYMM(trade_date)
ORDER BY (backtest_id, symbol, trade_date)
SETTINGS index_granularity = 8192;