#!/usr/bin/env python3
"""
AI量化交易工具系统主入口

这是系统的主要启动文件，负责初始化各个模块并启动Web服务。
"""

import asyncio
import sys
from pathlib import Path

import uvicorn
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.gzip import GZipMiddleware

# 添加src目录到Python路径
sys.path.insert(0, str(Path(__file__).parent / "src"))

from quantitative_tools.config import settings
from quantitative_tools.core.logging import setup_logging, get_logger

# 设置日志系统
setup_logging()
logger = get_logger("main")


def create_app() -> FastAPI:
    """创建FastAPI应用实例"""
    
    app = FastAPI(
        title=settings.app.app_name,
        description="专业的AI量化交易工具系统，提供完整的量化交易解决方案",
        version=settings.app.app_version,
        docs_url=settings.app.docs_url if not settings.app.environment == "production" else None,
        redoc_url=settings.app.redoc_url if not settings.app.environment == "production" else None,
        openapi_url=settings.app.openapi_url if not settings.app.environment == "production" else None,
    )
    
    # 添加CORS中间件
    app.add_middleware(
        CORSMiddleware,
        allow_origins=settings.app.cors_origins,
        allow_credentials=True,
        allow_methods=settings.app.cors_methods,
        allow_headers=settings.app.cors_headers,
    )
    
    # 添加Gzip压缩中间件
    app.add_middleware(GZipMiddleware, minimum_size=1000)
    
    # 注册路由
    register_routes(app)
    
    # 注册事件处理器
    register_events(app)
    
    logger.info("FastAPI应用创建完成", 
                应用名称=settings.app.app_name,
                版本=settings.app.app_version,
                环境=settings.app.environment)
    
    return app


def register_routes(app: FastAPI) -> None:
    """注册API路由"""
    
    @app.get("/")
    async def root():
        """根路径，返回系统信息"""
        return {
            "message": "欢迎使用AI量化交易工具系统",
            "version": settings.app.app_version,
            "status": "运行中",
            "docs_url": settings.app.docs_url,
        }
    
    @app.get("/health")
    async def health_check():
        """健康检查接口"""
        return {
            "status": "健康",
            "timestamp": "2024-01-01T00:00:00Z",
            "version": settings.app.app_version,
        }
    
    # TODO: 添加其他API路由
    # from quantitative_tools.api import auth, users, strategies, trading, data
    # app.include_router(auth.router, prefix=f"{settings.app.api_prefix}/auth", tags=["认证"])
    # app.include_router(users.router, prefix=f"{settings.app.api_prefix}/users", tags=["用户管理"])
    # app.include_router(strategies.router, prefix=f"{settings.app.api_prefix}/strategies", tags=["策略管理"])
    # app.include_router(trading.router, prefix=f"{settings.app.api_prefix}/trading", tags=["交易管理"])
    # app.include_router(data.router, prefix=f"{settings.app.api_prefix}/data", tags=["数据管理"])
    
    logger.info("API路由注册完成")


def register_events(app: FastAPI) -> None:
    """注册应用事件处理器"""
    
    @app.on_event("startup")
    async def startup_event():
        """应用启动事件"""
        logger.info("系统启动中...", 
                    主机=settings.app.host,
                    端口=settings.app.port,
                    工作进程数=settings.app.workers)
        
        # TODO: 初始化数据库连接
        # await init_database()
        
        # TODO: 初始化Redis连接
        # await init_redis()
        
        # TODO: 初始化消息队列
        # await init_message_queue()
        
        # TODO: 启动后台任务
        # await start_background_tasks()
        
        logger.info("系统启动完成")
    
    @app.on_event("shutdown")
    async def shutdown_event():
        """应用关闭事件"""
        logger.info("系统关闭中...")
        
        # TODO: 清理资源
        # await cleanup_resources()
        
        logger.info("系统关闭完成")


async def init_database():
    """初始化数据库连接"""
    logger.info("初始化数据库连接...", 
                postgres_host=settings.database.postgres_host,
                clickhouse_host=settings.database.clickhouse_host)
    # TODO: 实现数据库初始化逻辑


async def init_redis():
    """初始化Redis连接"""
    logger.info("初始化Redis连接...", 
                redis_host=settings.database.redis_host)
    # TODO: 实现Redis初始化逻辑


async def init_message_queue():
    """初始化消息队列"""
    logger.info("初始化消息队列...")
    # TODO: 实现消息队列初始化逻辑


async def start_background_tasks():
    """启动后台任务"""
    logger.info("启动后台任务...")
    # TODO: 启动数据采集、策略执行等后台任务


async def cleanup_resources():
    """清理系统资源"""
    logger.info("清理系统资源...")
    # TODO: 实现资源清理逻辑


def main():
    """主函数"""
    logger.info("启动AI量化交易工具系统", 
                版本=settings.app.app_version,
                环境=settings.app.environment,
                调试模式=settings.app.debug)
    
    # 创建应用实例
    app = create_app()
    
    # 启动服务器
    uvicorn.run(
        app,
        host=settings.app.host,
        port=settings.app.port,
        workers=settings.app.workers if not settings.app.debug else 1,
        reload=settings.app.debug,
        log_level=settings.monitoring.log_level.lower(),
        access_log=True,
    )


if __name__ == "__main__":
    main()
