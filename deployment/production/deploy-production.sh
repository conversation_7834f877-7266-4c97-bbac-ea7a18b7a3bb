#!/bin/bash

# 生产环境部署脚本
# 用于部署AI量化交易工具到生产环境

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置变量
ENVIRONMENT="production"
PROJECT_NAME="ai-quantitative-tools"
BACKUP_DIR="/backup/${PROJECT_NAME}"
LOG_DIR="/var/log/${PROJECT_NAME}"
DATA_DIR="/data/${PROJECT_NAME}"

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查运行权限
check_permissions() {
    if [[ $EUID -ne 0 ]]; then
        log_error "此脚本需要root权限运行"
        exit 1
    fi
}

# 检查系统要求
check_system_requirements() {
    log_info "检查系统要求..."
    
    # 检查操作系统
    if [[ ! -f /etc/os-release ]]; then
        log_error "无法确定操作系统版本"
        exit 1
    fi
    
    # 检查内存
    MEMORY_GB=$(free -g | awk '/^Mem:/{print $2}')
    if [[ $MEMORY_GB -lt 8 ]]; then
        log_warning "建议至少8GB内存，当前: ${MEMORY_GB}GB"
    fi
    
    # 检查磁盘空间
    DISK_SPACE=$(df -BG / | awk 'NR==2{print $4}' | sed 's/G//')
    if [[ $DISK_SPACE -lt 50 ]]; then
        log_warning "建议至少50GB磁盘空间，当前可用: ${DISK_SPACE}GB"
    fi
    
    log_success "系统要求检查完成"
}

# 安装系统依赖
install_system_dependencies() {
    log_info "安装系统依赖..."
    
    # 更新包管理器
    apt-get update -y
    
    # 安装基础工具
    apt-get install -y \
        curl \
        wget \
        git \
        vim \
        htop \
        unzip \
        software-properties-common \
        apt-transport-https \
        ca-certificates \
        gnupg \
        lsb-release
    
    # 安装Docker
    if ! command -v docker &> /dev/null; then
        log_info "安装Docker..."
        curl -fsSL https://download.docker.com/linux/ubuntu/gpg | gpg --dearmor -o /usr/share/keyrings/docker-archive-keyring.gpg
        echo "deb [arch=$(dpkg --print-architecture) signed-by=/usr/share/keyrings/docker-archive-keyring.gpg] https://download.docker.com/linux/ubuntu $(lsb_release -cs) stable" | tee /etc/apt/sources.list.d/docker.list > /dev/null
        apt-get update -y
        apt-get install -y docker-ce docker-ce-cli containerd.io docker-compose-plugin
        systemctl enable docker
        systemctl start docker
    fi
    
    # 安装Docker Compose
    if ! command -v docker-compose &> /dev/null; then
        log_info "安装Docker Compose..."
        curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
        chmod +x /usr/local/bin/docker-compose
    fi
    
    log_success "系统依赖安装完成"
}

# 创建目录结构
create_directories() {
    log_info "创建目录结构..."
    
    # 创建必要目录
    mkdir -p $BACKUP_DIR
    mkdir -p $LOG_DIR
    mkdir -p $DATA_DIR
    mkdir -p /etc/${PROJECT_NAME}
    mkdir -p /opt/${PROJECT_NAME}
    
    # 设置权限
    chown -R 1000:1000 $DATA_DIR
    chown -R 1000:1000 $LOG_DIR
    
    log_success "目录结构创建完成"
}

# 配置防火墙
configure_firewall() {
    log_info "配置防火墙..."
    
    # 安装ufw
    apt-get install -y ufw
    
    # 配置防火墙规则
    ufw --force reset
    ufw default deny incoming
    ufw default allow outgoing
    
    # 允许SSH
    ufw allow ssh
    
    # 允许HTTP和HTTPS
    ufw allow 80/tcp
    ufw allow 443/tcp
    
    # 允许应用端口
    ufw allow 8000/tcp  # API网关
    ufw allow 3000/tcp  # 前端
    ufw allow 9090/tcp  # Prometheus
    ufw allow 3001/tcp  # Grafana
    
    # 启用防火墙
    ufw --force enable
    
    log_success "防火墙配置完成"
}

# 配置SSL证书
configure_ssl() {
    log_info "配置SSL证书..."
    
    # 安装Certbot
    apt-get install -y certbot python3-certbot-nginx
    
    # 这里应该配置实际的域名和证书
    # certbot --nginx -d your-domain.com
    
    log_warning "请手动配置SSL证书: certbot --nginx -d your-domain.com"
}

# 部署应用
deploy_application() {
    log_info "部署应用..."
    
    # 切换到项目目录
    cd /opt/${PROJECT_NAME}
    
    # 拉取最新代码
    if [[ -d .git ]]; then
        git pull origin main
    else
        git clone https://github.com/your-repo/${PROJECT_NAME}.git .
    fi
    
    # 复制生产环境配置
    cp deployment/production/docker-compose.prod.yml docker-compose.yml
    cp deployment/production/.env.prod .env
    
    # 构建和启动服务
    docker-compose down
    docker-compose build --no-cache
    docker-compose up -d
    
    log_success "应用部署完成"
}

# 配置监控
configure_monitoring() {
    log_info "配置监控..."
    
    # 配置Prometheus
    mkdir -p /etc/prometheus
    cp monitoring/prometheus/prometheus.prod.yml /etc/prometheus/prometheus.yml
    
    # 配置Grafana
    mkdir -p /etc/grafana
    cp -r monitoring/grafana/* /etc/grafana/
    
    # 配置日志轮转
    cat > /etc/logrotate.d/${PROJECT_NAME} << EOF
${LOG_DIR}/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 1000 1000
    postrotate
        docker-compose -f /opt/${PROJECT_NAME}/docker-compose.yml restart app
    endscript
}
EOF
    
    log_success "监控配置完成"
}

# 配置备份
configure_backup() {
    log_info "配置备份..."
    
    # 创建备份脚本
    cat > /usr/local/bin/${PROJECT_NAME}-backup.sh << 'EOF'
#!/bin/bash

BACKUP_DIR="/backup/ai-quantitative-tools"
DATE=$(date +%Y%m%d_%H%M%S)
PROJECT_DIR="/opt/ai-quantitative-tools"

# 创建备份目录
mkdir -p $BACKUP_DIR/$DATE

# 备份数据库
docker exec quantitative-tools-postgres pg_dump -U postgres quantitative_tools > $BACKUP_DIR/$DATE/postgres_backup.sql

# 备份ClickHouse
docker exec quantitative-tools-clickhouse clickhouse-client --query "BACKUP DATABASE market_data TO Disk('default', '$DATE/clickhouse_backup')"

# 备份配置文件
cp -r $PROJECT_DIR/.env $BACKUP_DIR/$DATE/
cp -r $PROJECT_DIR/docker-compose.yml $BACKUP_DIR/$DATE/

# 压缩备份
cd $BACKUP_DIR
tar -czf ${DATE}.tar.gz $DATE/
rm -rf $DATE/

# 清理旧备份（保留30天）
find $BACKUP_DIR -name "*.tar.gz" -mtime +30 -delete

echo "备份完成: $BACKUP_DIR/${DATE}.tar.gz"
EOF
    
    chmod +x /usr/local/bin/${PROJECT_NAME}-backup.sh
    
    # 配置定时备份
    cat > /etc/cron.d/${PROJECT_NAME}-backup << EOF
# 每天凌晨2点执行备份
0 2 * * * root /usr/local/bin/${PROJECT_NAME}-backup.sh >> /var/log/${PROJECT_NAME}/backup.log 2>&1
EOF
    
    log_success "备份配置完成"
}

# 配置健康检查
configure_health_checks() {
    log_info "配置健康检查..."
    
    # 创建健康检查脚本
    cat > /usr/local/bin/${PROJECT_NAME}-health-check.sh << 'EOF'
#!/bin/bash

PROJECT_NAME="ai-quantitative-tools"
LOG_FILE="/var/log/${PROJECT_NAME}/health-check.log"

# 检查Docker容器状态
check_containers() {
    local failed_containers=()
    
    containers=("quantitative-tools-app" "quantitative-tools-postgres" "quantitative-tools-redis" "quantitative-tools-nginx")
    
    for container in "${containers[@]}"; do
        if ! docker ps | grep -q "$container"; then
            failed_containers+=("$container")
        fi
    done
    
    if [ ${#failed_containers[@]} -gt 0 ]; then
        echo "$(date): 容器健康检查失败: ${failed_containers[*]}" >> $LOG_FILE
        return 1
    fi
    
    return 0
}

# 检查API健康状态
check_api_health() {
    local response=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:8000/health)
    
    if [ "$response" != "200" ]; then
        echo "$(date): API健康检查失败: HTTP $response" >> $LOG_FILE
        return 1
    fi
    
    return 0
}

# 检查数据库连接
check_database() {
    if ! docker exec quantitative-tools-postgres pg_isready -U postgres > /dev/null 2>&1; then
        echo "$(date): 数据库健康检查失败" >> $LOG_FILE
        return 1
    fi
    
    return 0
}

# 执行所有检查
if check_containers && check_api_health && check_database; then
    echo "$(date): 健康检查通过" >> $LOG_FILE
    exit 0
else
    echo "$(date): 健康检查失败" >> $LOG_FILE
    exit 1
fi
EOF
    
    chmod +x /usr/local/bin/${PROJECT_NAME}-health-check.sh
    
    # 配置定时健康检查
    cat > /etc/cron.d/${PROJECT_NAME}-health-check << EOF
# 每5分钟执行健康检查
*/5 * * * * root /usr/local/bin/${PROJECT_NAME}-health-check.sh
EOF
    
    log_success "健康检查配置完成"
}

# 优化系统性能
optimize_system() {
    log_info "优化系统性能..."
    
    # 优化内核参数
    cat >> /etc/sysctl.conf << EOF

# AI量化交易工具优化参数
net.core.somaxconn = 65535
net.core.netdev_max_backlog = 5000
net.ipv4.tcp_max_syn_backlog = 65535
net.ipv4.tcp_fin_timeout = 30
net.ipv4.tcp_keepalive_time = 1200
net.ipv4.tcp_max_tw_buckets = 5000
vm.swappiness = 10
vm.dirty_ratio = 15
vm.dirty_background_ratio = 5
EOF
    
    sysctl -p
    
    # 优化文件描述符限制
    cat >> /etc/security/limits.conf << EOF

# AI量化交易工具文件描述符限制
* soft nofile 65535
* hard nofile 65535
root soft nofile 65535
root hard nofile 65535
EOF
    
    log_success "系统性能优化完成"
}

# 主部署函数
main() {
    echo "=========================================="
    echo "  AI量化交易工具生产环境部署脚本"
    echo "=========================================="
    echo ""
    
    check_permissions
    check_system_requirements
    install_system_dependencies
    create_directories
    configure_firewall
    configure_ssl
    deploy_application
    configure_monitoring
    configure_backup
    configure_health_checks
    optimize_system
    
    echo ""
    log_success "🎉 生产环境部署完成！"
    echo ""
    echo "📝 后续步骤："
    echo "   1. 配置域名DNS解析"
    echo "   2. 申请SSL证书: certbot --nginx -d your-domain.com"
    echo "   3. 配置监控告警通知"
    echo "   4. 测试备份和恢复流程"
    echo "   5. 进行安全扫描和渗透测试"
    echo ""
    echo "🔗 访问地址："
    echo "   - 主应用: https://your-domain.com"
    echo "   - 监控面板: https://your-domain.com:3001"
    echo "   - API文档: https://your-domain.com/docs"
    echo ""
}

# 处理命令行参数
case "${1:-}" in
    "rollback")
        log_info "执行回滚操作..."
        # 实现回滚逻辑
        ;;
    "update")
        log_info "执行更新操作..."
        deploy_application
        ;;
    "backup")
        log_info "执行备份操作..."
        /usr/local/bin/${PROJECT_NAME}-backup.sh
        ;;
    "health")
        log_info "执行健康检查..."
        /usr/local/bin/${PROJECT_NAME}-health-check.sh
        ;;
    *)
        main
        ;;
esac
