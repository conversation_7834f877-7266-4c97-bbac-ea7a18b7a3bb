# AI量化交易工具系统 Docker Compose配置
# 包含所有必要的服务组件

version: '3.8'

services:
  # 主应用服务
  app:
    build: .
    container_name: quantitative-tools-app
    ports:
      - "8000:8000"
    environment:
      - POSTGRES_HOST=postgres
      - CLICKHOUSE_HOST=clickhouse
      - REDIS_HOST=redis
      - KAFKA_BOOTSTRAP_SERVERS=kafka:9092
    depends_on:
      - postgres
      - clickhouse
      - redis
      - kafka
    volumes:
      - ./logs:/app/logs
      - ./uploads:/app/uploads
      - ./data:/app/data
    networks:
      - quantitative-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # PostgreSQL主数据库
  postgres:
    image: postgres:15
    container_name: quantitative-tools-postgres
    environment:
      POSTGRES_DB: quantitative_tools
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres123
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8 --lc-collate=C --lc-ctype=C"
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./scripts/init-postgres.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - quantitative-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 10s
      timeout: 5s
      retries: 5

  # ClickHouse时序数据库
  clickhouse:
    image: clickhouse/clickhouse-server:23.8
    container_name: quantitative-tools-clickhouse
    environment:
      CLICKHOUSE_DB: market_data
      CLICKHOUSE_USER: default
      CLICKHOUSE_PASSWORD: clickhouse123
    ports:
      - "9000:9000"
      - "8123:8123"
    volumes:
      - clickhouse_data:/var/lib/clickhouse
      - ./scripts/init-clickhouse.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - quantitative-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:8123/ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis缓存数据库
  redis:
    image: redis:7-alpine
    container_name: quantitative-tools-redis
    command: redis-server --requirepass redis123 --appendonly yes
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - quantitative-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Kafka消息队列
  zookeeper:
    image: confluentinc/cp-zookeeper:7.4.0
    container_name: quantitative-tools-zookeeper
    environment:
      ZOOKEEPER_CLIENT_PORT: 2181
      ZOOKEEPER_TICK_TIME: 2000
    networks:
      - quantitative-network
    restart: unless-stopped

  kafka:
    image: confluentinc/cp-kafka:7.4.0
    container_name: quantitative-tools-kafka
    depends_on:
      - zookeeper
    ports:
      - "9092:9092"
    environment:
      KAFKA_BROKER_ID: 1
      KAFKA_ZOOKEEPER_CONNECT: zookeeper:2181
      KAFKA_ADVERTISED_LISTENERS: PLAINTEXT://kafka:29092,PLAINTEXT_HOST://localhost:9092
      KAFKA_LISTENER_SECURITY_PROTOCOL_MAP: PLAINTEXT:PLAINTEXT,PLAINTEXT_HOST:PLAINTEXT
      KAFKA_INTER_BROKER_LISTENER_NAME: PLAINTEXT
      KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR: 1
      KAFKA_AUTO_CREATE_TOPICS_ENABLE: true
    volumes:
      - kafka_data:/var/lib/kafka/data
    networks:
      - quantitative-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "kafka-broker-api-versions", "--bootstrap-server", "localhost:9092"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Prometheus监控
  prometheus:
    image: prom/prometheus:v2.47.0
    container_name: quantitative-tools-prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    networks:
      - quantitative-network
    restart: unless-stopped

  # Grafana可视化
  grafana:
    image: grafana/grafana:10.1.0
    container_name: quantitative-tools-grafana
    ports:
      - "3000:3000"
    environment:
      GF_SECURITY_ADMIN_PASSWORD: admin123
      GF_INSTALL_PLUGINS: grafana-clock-panel,grafana-simple-json-datasource
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources
    networks:
      - quantitative-network
    restart: unless-stopped
    depends_on:
      - prometheus

  # Nginx反向代理
  nginx:
    image: nginx:1.25-alpine
    container_name: quantitative-tools-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/ssl:/etc/nginx/ssl
      - ./static:/var/www/static
    depends_on:
      - app
    networks:
      - quantitative-network
    restart: unless-stopped

  # MinIO对象存储
  minio:
    image: minio/minio:RELEASE.2023-09-04T19-57-37Z
    container_name: quantitative-tools-minio
    ports:
      - "9001:9001"
      - "9002:9002"
    environment:
      MINIO_ROOT_USER: minioadmin
      MINIO_ROOT_PASSWORD: minioadmin123
    volumes:
      - minio_data:/data
    command: server /data --console-address ":9001" --address ":9002"
    networks:
      - quantitative-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9002/minio/health/live"]
      interval: 30s
      timeout: 20s
      retries: 3

  # Elasticsearch日志存储
  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:8.9.0
    container_name: quantitative-tools-elasticsearch
    environment:
      - discovery.type=single-node
      - xpack.security.enabled=false
      - "ES_JAVA_OPTS=-Xms512m -Xmx512m"
    ports:
      - "9200:9200"
    volumes:
      - elasticsearch_data:/usr/share/elasticsearch/data
    networks:
      - quantitative-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:9200/_cluster/health || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Kibana日志可视化
  kibana:
    image: docker.elastic.co/kibana/kibana:8.9.0
    container_name: quantitative-tools-kibana
    environment:
      ELASTICSEARCH_HOSTS: http://elasticsearch:9200
    ports:
      - "5601:5601"
    depends_on:
      - elasticsearch
    networks:
      - quantitative-network
    restart: unless-stopped

# 数据卷定义
volumes:
  postgres_data:
    driver: local
  clickhouse_data:
    driver: local
  redis_data:
    driver: local
  kafka_data:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local
  minio_data:
    driver: local
  elasticsearch_data:
    driver: local

# 网络定义
networks:
  quantitative-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
