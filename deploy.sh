#!/bin/bash

# AI量化交易工具系统部署脚本
# 自动化部署整个系统

set -e

echo "🚀 开始部署AI量化交易工具系统..."

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查依赖
check_dependencies() {
    log_info "检查系统依赖..."
    
    # 检查Docker
    if ! command -v docker &> /dev/null; then
        log_error "Docker未安装，请先安装Docker"
        exit 1
    fi
    
    # 检查Docker Compose
    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose未安装，请先安装Docker Compose"
        exit 1
    fi
    
    # 检查Python
    if ! command -v python3 &> /dev/null; then
        log_error "Python3未安装，请先安装Python3"
        exit 1
    fi
    
    # 检查Node.js (如果需要构建前端)
    if ! command -v node &> /dev/null; then
        log_warning "Node.js未安装，将跳过前端构建"
    fi
    
    log_success "依赖检查完成"
}

# 创建必要的目录
create_directories() {
    log_info "创建必要的目录结构..."
    
    mkdir -p logs
    mkdir -p uploads
    mkdir -p data
    mkdir -p monitoring/prometheus
    mkdir -p monitoring/grafana/{dashboards,datasources}
    mkdir -p nginx
    mkdir -p scripts
    
    log_success "目录创建完成"
}

# 生成配置文件
generate_configs() {
    log_info "生成配置文件..."
    
    # 生成Prometheus配置
    cat > monitoring/prometheus/prometheus.yml << EOF
global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  # - "first_rules.yml"
  # - "second_rules.yml"

scrape_configs:
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']

  - job_name: 'quantitative-app'
    static_configs:
      - targets: ['app:8000']
    metrics_path: '/metrics'
    scrape_interval: 30s

  - job_name: 'user-service'
    static_configs:
      - targets: ['user-service:8001']
    metrics_path: '/metrics'
    scrape_interval: 30s

  - job_name: 'data-service'
    static_configs:
      - targets: ['data-service:8002']
    metrics_path: '/metrics'
    scrape_interval: 30s
EOF

    # 生成Nginx配置
    cat > nginx/nginx.conf << EOF
events {
    worker_connections 1024;
}

http {
    upstream app_backend {
        server app:8000;
    }
    
    upstream user_service {
        server user-service:8001;
    }
    
    upstream data_service {
        server data-service:8002;
    }

    server {
        listen 80;
        server_name localhost;

        location / {
            proxy_pass http://app_backend;
            proxy_set_header Host \$host;
            proxy_set_header X-Real-IP \$remote_addr;
            proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto \$scheme;
        }

        location /api/v1/auth/ {
            proxy_pass http://user_service;
            proxy_set_header Host \$host;
            proxy_set_header X-Real-IP \$remote_addr;
        }

        location /api/v1/data/ {
            proxy_pass http://data_service;
            proxy_set_header Host \$host;
            proxy_set_header X-Real-IP \$remote_addr;
        }

        location /ws/ {
            proxy_pass http://data_service;
            proxy_http_version 1.1;
            proxy_set_header Upgrade \$http_upgrade;
            proxy_set_header Connection "upgrade";
            proxy_set_header Host \$host;
        }
    }
}
EOF

    log_success "配置文件生成完成"
}

# 构建Docker镜像
build_images() {
    log_info "构建Docker镜像..."
    
    # 构建主应用镜像
    if [ -f "Dockerfile" ]; then
        docker build -t quantitative-tools:latest .
        log_success "主应用镜像构建完成"
    fi
    
    # 构建用户服务镜像
    if [ -f "backend/services/user-service/Dockerfile" ]; then
        docker build -t quantitative-user-service:latest backend/services/user-service/
        log_success "用户服务镜像构建完成"
    fi
    
    # 构建数据服务镜像
    if [ -f "backend/services/data-service/Dockerfile" ]; then
        docker build -t quantitative-data-service:latest backend/services/data-service/
        log_success "数据服务镜像构建完成"
    fi
    
    # 构建前端镜像
    if [ -f "frontend/Dockerfile" ]; then
        docker build -t quantitative-frontend:latest frontend/
        log_success "前端镜像构建完成"
    fi
}

# 启动服务
start_services() {
    log_info "启动系统服务..."
    
    # 停止现有服务
    docker-compose down
    
    # 启动基础设施服务
    log_info "启动基础设施服务..."
    docker-compose up -d postgres clickhouse redis zookeeper kafka
    
    # 等待基础设施服务启动
    log_info "等待基础设施服务启动..."
    sleep 30
    
    # 启动应用服务
    log_info "启动应用服务..."
    docker-compose up -d app user-service data-service
    
    # 等待应用服务启动
    sleep 20
    
    # 启动监控服务
    log_info "启动监控服务..."
    docker-compose up -d prometheus grafana elasticsearch kibana
    
    # 启动代理服务
    log_info "启动代理服务..."
    docker-compose up -d nginx
    
    log_success "所有服务启动完成"
}

# 检查服务状态
check_services() {
    log_info "检查服务状态..."
    
    services=("postgres" "clickhouse" "redis" "kafka" "app" "user-service" "data-service" "prometheus" "grafana" "nginx")
    
    for service in "${services[@]}"; do
        if docker-compose ps | grep -q "$service.*Up"; then
            log_success "$service 服务运行正常"
        else
            log_error "$service 服务启动失败"
        fi
    done
}

# 显示访问信息
show_access_info() {
    log_info "系统访问信息："
    echo ""
    echo "🌐 主要服务："
    echo "   - 主应用: http://localhost"
    echo "   - 用户服务: http://localhost:8001"
    echo "   - 数据服务: http://localhost:8002"
    echo ""
    echo "📊 监控服务："
    echo "   - Prometheus: http://localhost:9090"
    echo "   - Grafana: http://localhost:3000 (admin/admin123)"
    echo "   - Kibana: http://localhost:5601"
    echo ""
    echo "💾 数据库："
    echo "   - PostgreSQL: localhost:5432 (postgres/postgres123)"
    echo "   - ClickHouse: localhost:8123 (default/clickhouse123)"
    echo "   - Redis: localhost:6379 (redis123)"
    echo ""
    echo "📨 消息队列："
    echo "   - Kafka: localhost:9092"
    echo ""
    echo "📁 对象存储："
    echo "   - MinIO: http://localhost:9001 (minioadmin/minioadmin123)"
    echo ""
}

# 主函数
main() {
    echo "=========================================="
    echo "  AI量化交易工具系统自动部署脚本"
    echo "=========================================="
    echo ""
    
    check_dependencies
    create_directories
    generate_configs
    build_images
    start_services
    
    echo ""
    log_info "等待服务完全启动..."
    sleep 30
    
    check_services
    show_access_info
    
    echo ""
    log_success "🎉 系统部署完成！"
    echo ""
    echo "📝 后续步骤："
    echo "   1. 访问 http://localhost 查看主应用"
    echo "   2. 访问 http://localhost:3000 配置Grafana监控面板"
    echo "   3. 查看日志: docker-compose logs -f [service-name]"
    echo "   4. 停止系统: docker-compose down"
    echo ""
}

# 处理命令行参数
case "${1:-}" in
    "stop")
        log_info "停止所有服务..."
        docker-compose down
        log_success "所有服务已停止"
        ;;
    "restart")
        log_info "重启所有服务..."
        docker-compose down
        docker-compose up -d
        log_success "所有服务已重启"
        ;;
    "logs")
        docker-compose logs -f "${2:-}"
        ;;
    "status")
        docker-compose ps
        ;;
    *)
        main
        ;;
esac
