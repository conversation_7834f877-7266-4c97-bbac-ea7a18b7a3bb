"""
基础功能测试模块

测试系统的基础功能，包括配置加载、日志系统、异常处理等。
"""

import pytest
import sys
from pathlib import Path

# 添加src目录到Python路径
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from quantitative_tools.config import settings
from quantitative_tools.core.logging import get_logger, setup_logging
from quantitative_tools.core.exceptions import (
    ValidationError,
    AuthenticationError,
    DataNotFoundError,
    BusinessLogicError,
)


class TestConfiguration:
    """配置系统测试"""
    
    def test_settings_loading(self):
        """测试配置加载"""
        assert settings is not None
        assert settings.app.app_name == "AI量化交易工具"
        assert settings.app.app_version == "1.0.0"
    
    def test_database_config(self):
        """测试数据库配置"""
        assert settings.database.postgres_host is not None
        assert settings.database.postgres_port == 5432
        assert settings.database.clickhouse_host is not None
        assert settings.database.redis_host is not None
    
    def test_security_config(self):
        """测试安全配置"""
        assert settings.security.algorithm == "HS256"
        assert settings.security.access_token_expire_minutes > 0
        assert settings.security.password_min_length >= 8
    
    def test_database_urls(self):
        """测试数据库连接URL生成"""
        postgres_url = settings.database.postgres_url
        assert "postgresql://" in postgres_url
        
        clickhouse_url = settings.database.clickhouse_url
        assert "clickhouse://" in clickhouse_url
        
        redis_url = settings.database.redis_url
        assert "redis://" in redis_url


class TestLogging:
    """日志系统测试"""
    
    def test_logger_creation(self):
        """测试日志记录器创建"""
        logger = get_logger("test")
        assert logger is not None
        assert logger.name == "test"
    
    def test_logging_methods(self):
        """测试日志记录方法"""
        logger = get_logger("test")
        
        # 测试基础日志方法
        logger.debug("调试信息测试")
        logger.info("信息记录测试")
        logger.warning("警告信息测试")
        logger.error("错误信息测试")
    
    def test_structured_logging(self):
        """测试结构化日志"""
        logger = get_logger("test")
        
        # 测试带参数的日志
        logger.info("用户登录", 用户ID=123, 用户名="test_user", IP地址="***********")
        
        # 测试业务日志方法
        logger.log_user_action(
            user_id=123,
            action="登录",
            resource="系统",
            result="成功"
        )
        
        logger.log_trading_action(
            user_id=123,
            symbol="000001.SZ",
            action="买入",
            quantity=1000,
            price=10.50
        )


class TestExceptions:
    """异常处理测试"""
    
    def test_base_exception(self):
        """测试基础异常"""
        with pytest.raises(ValidationError) as exc_info:
            raise ValidationError("测试验证错误", field="test_field", value="invalid")
        
        exception = exc_info.value
        assert exception.message == "测试验证错误"
        assert exception.error_code == "VALIDATION_ERROR"
        assert exception.details["field"] == "test_field"
        assert exception.details["value"] == "invalid"
    
    def test_authentication_error(self):
        """测试认证异常"""
        with pytest.raises(AuthenticationError) as exc_info:
            raise AuthenticationError("用户名或密码错误")
        
        exception = exc_info.value
        assert exception.message == "用户名或密码错误"
        assert exception.error_code == "AUTHENTICATION_ERROR"
    
    def test_data_not_found_error(self):
        """测试数据未找到异常"""
        with pytest.raises(DataNotFoundError) as exc_info:
            raise DataNotFoundError(
                "策略不存在",
                resource_type="strategy",
                resource_id=123
            )
        
        exception = exc_info.value
        assert exception.message == "策略不存在"
        assert exception.details["resource_type"] == "strategy"
        assert exception.details["resource_id"] == 123
    
    def test_business_logic_error(self):
        """测试业务逻辑异常"""
        with pytest.raises(BusinessLogicError) as exc_info:
            raise BusinessLogicError(
                "余额不足",
                business_rule="账户余额检查"
            )
        
        exception = exc_info.value
        assert exception.message == "余额不足"
        assert exception.details["business_rule"] == "账户余额检查"
    
    def test_exception_to_dict(self):
        """测试异常转换为字典"""
        exception = ValidationError(
            "参数验证失败",
            field="email",
            value="invalid-email",
            constraint="email_format"
        )
        
        error_dict = exception.to_dict()
        assert error_dict["error_code"] == "VALIDATION_ERROR"
        assert error_dict["message"] == "参数验证失败"
        assert error_dict["details"]["field"] == "email"
        assert error_dict["details"]["value"] == "invalid-email"
        assert error_dict["details"]["constraint"] == "email_format"


class TestSystemIntegration:
    """系统集成测试"""
    
    def test_system_initialization(self):
        """测试系统初始化"""
        # 测试日志系统初始化
        setup_logging()
        
        # 测试配置系统
        assert settings.get_all_settings() is not None
        
        # 测试日志记录
        logger = get_logger("integration_test")
        logger.info("系统集成测试", 测试状态="通过")
    
    def test_error_handling_integration(self):
        """测试错误处理集成"""
        logger = get_logger("error_test")
        
        try:
            raise ValidationError("集成测试错误", field="test")
        except ValidationError as e:
            logger.error("捕获到验证错误", 错误信息=e.message, 错误代码=e.error_code)
            assert e.error_code == "VALIDATION_ERROR"


if __name__ == "__main__":
    # 运行测试
    pytest.main([__file__, "-v", "--tb=short"])
