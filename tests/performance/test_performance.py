"""
性能测试和优化

测试系统各个组件的性能表现，包括：
- API接口性能测试
- 数据库查询性能测试
- 缓存性能测试
- 并发处理测试
- 内存使用测试
"""

import time
import asyncio
import threading
import concurrent.futures
import psutil
import pandas as pd
import numpy as np
from datetime import datetime, date, timedelta
import sys
import os

# 添加项目路径
sys.path.append(os.path.join(os.path.dirname(__file__), '../../src'))

from quantitative_tools.data.ingestion import DataIngestionEngine
from quantitative_tools.strategy.engine import StrategyEngine
from quantitative_tools.trading.orders import OrderManager
from quantitative_tools.trading.positions import PositionManager
from quantitative_tools.risk.calculator import RiskCalculator
from quantitative_tools.backtest.engine import BacktestEngine


class PerformanceProfiler:
    """性能分析器"""
    
    def __init__(self):
        self.results = {}
        
    def profile_function(self, func, *args, **kwargs):
        """分析函数性能"""
        # 记录开始状态
        start_time = time.time()
        start_memory = psutil.Process().memory_info().rss / 1024 / 1024  # MB
        
        # 执行函数
        result = func(*args, **kwargs)
        
        # 记录结束状态
        end_time = time.time()
        end_memory = psutil.Process().memory_info().rss / 1024 / 1024  # MB
        
        # 计算性能指标
        execution_time = end_time - start_time
        memory_usage = end_memory - start_memory
        
        return {
            'result': result,
            'execution_time': execution_time,
            'memory_usage': memory_usage,
            'start_memory': start_memory,
            'end_memory': end_memory
        }
    
    def benchmark_concurrent_execution(self, func, args_list, max_workers=4):
        """并发执行基准测试"""
        start_time = time.time()
        
        with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
            futures = [executor.submit(func, *args) for args in args_list]
            results = [future.result() for future in concurrent.futures.as_completed(futures)]
        
        end_time = time.time()
        total_time = end_time - start_time
        
        return {
            'results': results,
            'total_time': total_time,
            'avg_time_per_task': total_time / len(args_list),
            'tasks_per_second': len(args_list) / total_time
        }


class PerformanceTestSuite:
    """性能测试套件"""
    
    def __init__(self):
        self.profiler = PerformanceProfiler()
        self.data_engine = DataIngestionEngine()
        self.strategy_engine = StrategyEngine()
        self.order_manager = OrderManager()
        self.position_manager = PositionManager()
        self.risk_calculator = RiskCalculator()
        self.backtest_engine = BacktestEngine()
        
    def test_data_ingestion_performance(self):
        """测试数据接入性能"""
        print("\n=== 数据接入性能测试 ===")
        
        # 测试股票列表获取性能
        def get_stock_list():
            return self.data_engine.get_stock_list()
        
        result = self.profiler.profile_function(get_stock_list)
        print(f"股票列表获取:")
        print(f"  执行时间: {result['execution_time']:.3f}秒")
        print(f"  内存使用: {result['memory_usage']:.2f}MB")
        print(f"  返回记录: {len(result['result']) if not result['result'].empty else 0}条")
        
        # 测试实时数据获取性能
        symbols = ['000001.SZ', '000002.SZ', '600000.SH', '600036.SH']
        
        def get_realtime_data():
            return self.data_engine.get_realtime_data(symbols)
        
        result = self.profiler.profile_function(get_realtime_data)
        print(f"\n实时数据获取:")
        print(f"  执行时间: {result['execution_time']:.3f}秒")
        print(f"  内存使用: {result['memory_usage']:.2f}MB")
        print(f"  返回记录: {len(result['result']) if not result['result'].empty else 0}条")
        
        # 测试历史数据获取性能
        end_date = date.today()
        start_date = end_date - timedelta(days=30)
        
        def get_historical_data():
            return self.data_engine.get_daily_data('000001.SZ', start_date, end_date)
        
        result = self.profiler.profile_function(get_historical_data)
        print(f"\n历史数据获取:")
        print(f"  执行时间: {result['execution_time']:.3f}秒")
        print(f"  内存使用: {result['memory_usage']:.2f}MB")
        print(f"  返回记录: {len(result['result']) if not result['result'].empty else 0}条")
    
    def test_order_processing_performance(self):
        """测试订单处理性能"""
        print("\n=== 订单处理性能测试 ===")
        
        # 测试单个订单创建性能
        def create_single_order():
            from quantitative_tools.trading.orders import OrderSide, OrderType
            return self.order_manager.create_order(
                symbol='000001.SZ',
                side=OrderSide.BUY,
                quantity=100,
                order_type=OrderType.MARKET
            )
        
        result = self.profiler.profile_function(create_single_order)
        print(f"单个订单创建:")
        print(f"  执行时间: {result['execution_time']:.6f}秒")
        print(f"  内存使用: {result['memory_usage']:.2f}MB")
        
        # 测试批量订单创建性能
        def create_batch_orders():
            from quantitative_tools.trading.orders import OrderSide, OrderType
            orders = []
            for i in range(100):
                order = self.order_manager.create_order(
                    symbol=f'00000{i%5+1}.SZ',
                    side=OrderSide.BUY if i % 2 == 0 else OrderSide.SELL,
                    quantity=100,
                    order_type=OrderType.MARKET
                )
                orders.append(order)
            return orders
        
        result = self.profiler.profile_function(create_batch_orders)
        print(f"\n批量订单创建(100个):")
        print(f"  执行时间: {result['execution_time']:.3f}秒")
        print(f"  平均每个订单: {result['execution_time']/100:.6f}秒")
        print(f"  内存使用: {result['memory_usage']:.2f}MB")
        print(f"  订单创建速度: {100/result['execution_time']:.0f}个/秒")
    
    def test_position_calculation_performance(self):
        """测试仓位计算性能"""
        print("\n=== 仓位计算性能测试 ===")
        
        # 准备测试数据
        symbols = [f'00000{i}.SZ' for i in range(1, 101)]
        
        # 模拟交易更新仓位
        def update_positions():
            for i, symbol in enumerate(symbols):
                self.position_manager.update_trade(
                    symbol=symbol,
                    quantity=100 + i,
                    price=10 + i * 0.1,
                    side='buy',
                    commission=5.0
                )
                
                # 更新市场价格
                self.position_manager.update_market_price(
                    symbol=symbol,
                    price=10 + i * 0.1 + np.random.uniform(-0.5, 0.5)
                )
        
        result = self.profiler.profile_function(update_positions)
        print(f"仓位更新(100个股票):")
        print(f"  执行时间: {result['execution_time']:.3f}秒")
        print(f"  平均每个股票: {result['execution_time']/100:.6f}秒")
        print(f"  内存使用: {result['memory_usage']:.2f}MB")
        
        # 测试组合统计计算性能
        def calculate_portfolio_stats():
            return self.position_manager.get_portfolio_stats()
        
        result = self.profiler.profile_function(calculate_portfolio_stats)
        print(f"\n组合统计计算:")
        print(f"  执行时间: {result['execution_time']:.6f}秒")
        print(f"  内存使用: {result['memory_usage']:.2f}MB")
    
    def test_risk_calculation_performance(self):
        """测试风险计算性能"""
        print("\n=== 风险计算性能测试 ===")
        
        # 生成测试数据
        np.random.seed(42)
        
        # 测试不同数据量的风险计算性能
        data_sizes = [100, 500, 1000, 2500]
        
        for size in data_sizes:
            returns = pd.Series(np.random.normal(0.001, 0.02, size))
            prices = pd.Series((1 + returns).cumprod() * 100)
            
            def calculate_risk_metrics():
                return self.risk_calculator.calculate_risk_metrics(returns, prices)
            
            result = self.profiler.profile_function(calculate_risk_metrics)
            print(f"\n风险指标计算({size}个数据点):")
            print(f"  执行时间: {result['execution_time']:.6f}秒")
            print(f"  内存使用: {result['memory_usage']:.2f}MB")
            print(f"  处理速度: {size/result['execution_time']:.0f}个数据点/秒")
    
    def test_backtest_performance(self):
        """测试回测性能"""
        print("\n=== 回测性能测试 ===")
        
        from quantitative_tools.backtest.engine import BacktestConfig
        from quantitative_tools.strategy.base import BaseStrategy, StrategyContext
        
        # 创建简单测试策略
        class SimpleStrategy(BaseStrategy):
            def initialize(self):
                return True
            
            def on_data(self, data):
                pass
            
            def generate_signals(self):
                return []
        
        # 测试不同时间范围的回测性能
        test_periods = [
            (30, "1个月"),
            (90, "3个月"),
            (180, "6个月"),
            (365, "1年")
        ]
        
        for days, period_name in test_periods:
            end_date = date.today()
            start_date = end_date - timedelta(days=days)
            
            config = BacktestConfig(
                start_date=start_date,
                end_date=end_date,
                initial_capital=1000000
            )
            
            context = StrategyContext(
                strategy_id=f'perf_test_{days}',
                name=f'性能测试策略_{period_name}'
            )
            
            strategy = SimpleStrategy(context)
            
            def run_backtest():
                return self.backtest_engine.run_backtest(strategy, config)
            
            result = self.profiler.profile_function(run_backtest)
            print(f"\n回测性能({period_name}):")
            print(f"  执行时间: {result['execution_time']:.3f}秒")
            print(f"  内存使用: {result['memory_usage']:.2f}MB")
            print(f"  处理速度: {days/result['execution_time']:.1f}天/秒")
    
    def test_concurrent_performance(self):
        """测试并发性能"""
        print("\n=== 并发性能测试 ===")
        
        from quantitative_tools.trading.orders import OrderSide, OrderType
        
        # 测试并发订单创建
        def create_order_task(i):
            return self.order_manager.create_order(
                symbol=f'00000{i%5+1}.SZ',
                side=OrderSide.BUY,
                quantity=100,
                order_type=OrderType.MARKET
            )
        
        # 准备任务参数
        task_args = [(i,) for i in range(50)]
        
        # 测试不同并发度
        for workers in [1, 2, 4, 8]:
            result = self.profiler.benchmark_concurrent_execution(
                create_order_task, task_args, max_workers=workers
            )
            
            print(f"\n并发订单创建({workers}个线程):")
            print(f"  总执行时间: {result['total_time']:.3f}秒")
            print(f"  平均每个任务: {result['avg_time_per_task']:.6f}秒")
            print(f"  处理速度: {result['tasks_per_second']:.1f}个任务/秒")
    
    def test_memory_usage(self):
        """测试内存使用情况"""
        print("\n=== 内存使用测试 ===")
        
        process = psutil.Process()
        initial_memory = process.memory_info().rss / 1024 / 1024  # MB
        
        print(f"初始内存使用: {initial_memory:.2f}MB")
        
        # 创建大量数据对象
        large_data = []
        for i in range(1000):
            df = pd.DataFrame({
                'price': np.random.uniform(10, 100, 1000),
                'volume': np.random.randint(1000, 100000, 1000)
            })
            large_data.append(df)
        
        current_memory = process.memory_info().rss / 1024 / 1024
        print(f"创建大量数据后: {current_memory:.2f}MB (+{current_memory-initial_memory:.2f}MB)")
        
        # 清理数据
        del large_data
        
        final_memory = process.memory_info().rss / 1024 / 1024
        print(f"清理数据后: {final_memory:.2f}MB")
        
        # 内存使用建议
        if final_memory > initial_memory * 1.5:
            print("⚠️  警告: 可能存在内存泄漏")
        else:
            print("✓ 内存使用正常")
    
    def generate_performance_report(self):
        """生成性能报告"""
        print("\n" + "="*50)
        print("性能测试报告")
        print("="*50)
        
        # 系统信息
        print(f"CPU核心数: {psutil.cpu_count()}")
        print(f"内存总量: {psutil.virtual_memory().total / 1024 / 1024 / 1024:.1f}GB")
        print(f"Python版本: {sys.version}")
        
        # 性能建议
        print("\n性能优化建议:")
        print("1. 对于大量数据处理，考虑使用批处理")
        print("2. 合理设置并发线程数，避免过度并发")
        print("3. 定期清理不需要的数据对象")
        print("4. 使用缓存减少重复计算")
        print("5. 考虑使用异步处理提高响应速度")


def run_performance_tests():
    """运行性能测试"""
    print("🚀 开始性能测试...")
    
    test_suite = PerformanceTestSuite()
    
    try:
        # 运行各项性能测试
        test_suite.test_data_ingestion_performance()
        test_suite.test_order_processing_performance()
        test_suite.test_position_calculation_performance()
        test_suite.test_risk_calculation_performance()
        test_suite.test_backtest_performance()
        test_suite.test_concurrent_performance()
        test_suite.test_memory_usage()
        
        # 生成性能报告
        test_suite.generate_performance_report()
        
        print("\n🎉 性能测试完成！")
        return True
        
    except Exception as e:
        print(f"\n❌ 性能测试失败: {e}")
        return False


if __name__ == "__main__":
    success = run_performance_tests()
    exit(0 if success else 1)
