"""
系统集成测试

测试各个模块之间的集成和交互，确保系统功能的正确性和稳定性。
包括：
- 数据流集成测试
- 策略执行集成测试
- 交易流程集成测试
- 风险管理集成测试
- 回测分析集成测试
"""

import pytest
import asyncio
import pandas as pd
import numpy as np
from datetime import datetime, date, timedelta
import sys
import os

# 添加项目路径
sys.path.append(os.path.join(os.path.dirname(__file__), '../../src'))

from quantitative_tools.data.ingestion import DataIngestionEngine
from quantitative_tools.data.adapters import <PERSON><PERSON><PERSON>Adapter, YahooFinanceAdapter
from quantitative_tools.strategy.base import BaseStrategy, StrategyContext, StrategyState
from quantitative_tools.strategy.engine import StrategyEngine
from quantitative_tools.trading.orders import OrderManager, OrderType, OrderSide
from quantitative_tools.trading.positions import PositionManager
from quantitative_tools.trading.execution import TradeExecutor
from quantitative_tools.risk.calculator import RiskCalculator
from quantitative_tools.risk.rules import RiskRuleEngine
from quantitative_tools.risk.monitor import RiskMonitor
from quantitative_tools.backtest.engine import BacktestEngine, BacktestConfig


class TestStrategy(BaseStrategy):
    """测试策略"""
    
    def initialize(self) -> bool:
        self.lookback_period = self.get_parameter('lookback_period', 20)
        self.threshold = self.get_parameter('threshold', 0.02)
        return True
    
    def on_data(self, data):
        self._data_cache.update(data)
    
    def generate_signals(self):
        signals = []
        
        # 简单的移动平均策略
        for symbol, df in self._data_cache.items():
            if len(df) >= self.lookback_period:
                ma = df['close'].rolling(self.lookback_period).mean().iloc[-1]
                current_price = df['close'].iloc[-1]
                
                if current_price > ma * (1 + self.threshold):
                    signals.append({
                        'symbol': symbol,
                        'action': 'buy',
                        'quantity': 100,
                        'price': current_price,
                        'strategy_id': self.context.strategy_id
                    })
                elif current_price < ma * (1 - self.threshold):
                    signals.append({
                        'symbol': symbol,
                        'action': 'sell',
                        'quantity': 100,
                        'price': current_price,
                        'strategy_id': self.context.strategy_id
                    })
        
        return signals


class TestSystemIntegration:
    """系统集成测试类"""
    
    @pytest.fixture(autouse=True)
    def setup(self):
        """测试设置"""
        self.data_engine = DataIngestionEngine()
        self.strategy_engine = StrategyEngine()
        self.order_manager = OrderManager()
        self.position_manager = PositionManager(initial_cash=1000000)
        self.trade_executor = TradeExecutor(self.order_manager, self.position_manager)
        self.risk_calculator = RiskCalculator()
        self.risk_rule_engine = RiskRuleEngine()
        self.risk_monitor = RiskMonitor()
        self.backtest_engine = BacktestEngine()
        
        # 初始化数据引擎
        tushare_adapter = TushareAdapter()
        self.data_engine.register_adapter(tushare_adapter, is_primary=True)
        self.data_engine.initialize()
    
    def test_data_flow_integration(self):
        """测试数据流集成"""
        print("\n=== 测试数据流集成 ===")
        
        # 测试数据接入
        symbols = ['000001.SZ', '000002.SZ']
        
        # 获取股票列表
        stock_list = self.data_engine.get_stock_list()
        assert not stock_list.empty, "股票列表不应为空"
        print(f"✓ 股票列表获取成功: {len(stock_list)} 只股票")
        
        # 获取实时数据
        realtime_data = self.data_engine.get_realtime_data(symbols)
        assert not realtime_data.empty, "实时数据不应为空"
        print(f"✓ 实时数据获取成功: {len(realtime_data)} 条记录")
        
        # 获取历史数据
        end_date = date.today()
        start_date = end_date - timedelta(days=30)
        
        for symbol in symbols:
            daily_data = self.data_engine.get_daily_data(symbol, start_date, end_date)
            assert not daily_data.empty, f"{symbol} 历史数据不应为空"
            print(f"✓ {symbol} 历史数据获取成功: {len(daily_data)} 条记录")
    
    def test_strategy_execution_integration(self):
        """测试策略执行集成"""
        print("\n=== 测试策略执行集成 ===")
        
        # 创建测试策略
        context = StrategyContext(
            strategy_id='test_integration_001',
            name='集成测试策略',
            parameters={'lookback_period': 10, 'threshold': 0.01}
        )
        
        strategy = TestStrategy(context)
        
        # 注册策略到引擎
        success = self.strategy_engine.register_strategy(strategy)
        assert success, "策略注册应该成功"
        print("✓ 策略注册成功")
        
        # 启动策略引擎
        success = self.strategy_engine.start_engine()
        assert success, "策略引擎启动应该成功"
        print("✓ 策略引擎启动成功")
        
        # 启动策略
        success = self.strategy_engine.start_strategy('test_integration_001')
        assert success, "策略启动应该成功"
        print("✓ 策略启动成功")
        
        # 检查策略状态
        status = self.strategy_engine.get_strategy_status('test_integration_001')
        assert status is not None, "策略状态不应为空"
        assert status['state'] == StrategyState.RUNNING.value, "策略应该处于运行状态"
        print("✓ 策略状态检查通过")
        
        # 模拟数据更新
        mock_data = {
            '000001.SZ': pd.DataFrame({
                'close': np.random.uniform(10, 12, 25),
                'volume': np.random.randint(1000000, 5000000, 25)
            })
        }
        
        strategy.on_data(mock_data)
        signals = strategy.generate_signals()
        print(f"✓ 信号生成成功: {len(signals)} 个信号")
        
        # 停止策略
        success = self.strategy_engine.stop_strategy('test_integration_001')
        assert success, "策略停止应该成功"
        print("✓ 策略停止成功")
    
    def test_trading_flow_integration(self):
        """测试交易流程集成"""
        print("\n=== 测试交易流程集成 ===")
        
        # 测试订单创建和执行
        order = self.order_manager.create_order(
            symbol='000001.SZ',
            side=OrderSide.BUY,
            quantity=100,
            order_type=OrderType.MARKET,
            strategy_id='test_integration_001'
        )
        
        assert order is not None, "订单创建不应失败"
        print(f"✓ 订单创建成功: {order.order_id}")
        
        # 提交订单
        success = self.order_manager.submit_order(order.order_id)
        assert success, "订单提交应该成功"
        print("✓ 订单提交成功")
        
        # 检查订单状态
        updated_order = self.order_manager.get_order(order.order_id)
        assert updated_order is not None, "订单查询不应失败"
        print(f"✓ 订单状态: {updated_order.status.value}")
        
        # 测试交易执行器
        signal = {
            'symbol': '000002.SZ',
            'action': 'buy',
            'quantity': 200,
            'price': 15.0,
            'strategy_id': 'test_integration_001'
        }
        
        executed_order = self.trade_executor.execute_signal(signal)
        assert executed_order is not None, "信号执行不应失败"
        print(f"✓ 信号执行成功: {executed_order.order_id}")
        
        # 检查仓位更新
        position = self.position_manager.get_position('000002.SZ')
        if position:
            print(f"✓ 仓位更新: {position.symbol} 数量:{position.quantity}")
        
        # 获取组合统计
        portfolio_stats = self.position_manager.get_portfolio_stats()
        print(f"✓ 组合价值: {portfolio_stats['portfolio_value']:,.0f}")
        print(f"✓ 现金余额: {portfolio_stats['cash']:,.0f}")
    
    def test_risk_management_integration(self):
        """测试风险管理集成"""
        print("\n=== 测试风险管理集成 ===")
        
        # 生成模拟收益率数据
        np.random.seed(42)
        returns = pd.Series(np.random.normal(0.001, 0.02, 100))
        prices = pd.Series((1 + returns).cumprod() * 100)
        
        # 测试风险计算
        risk_metrics = self.risk_calculator.calculate_risk_metrics(returns, prices)
        assert risk_metrics, "风险指标计算不应为空"
        print(f"✓ VaR 95%: {risk_metrics.get('var_95', 0):.4f}")
        print(f"✓ 最大回撤: {risk_metrics.get('max_drawdown_pct', 0):.2f}%")
        print(f"✓ 夏普比率: {risk_metrics.get('sharpe_ratio', 0):.2f}")
        
        # 测试风控规则
        portfolio_context = {
            'portfolio_value': 1000000,
            'cash': 200000,
            'positions': {
                '000001.SZ': {'market_value': 300000},  # 30%持仓，超过20%限制
                '000002.SZ': {'market_value': 150000}
            },
            'daily_trades': 50,
            'max_drawdown': 0.08
        }
        
        violations = self.risk_rule_engine.check_all_rules(portfolio_context)
        print(f"✓ 风控检查完成: {len(violations)} 个违规")
        
        for violation in violations:
            print(f"  - {violation.rule_name}: {violation.message}")
        
        # 获取规则统计
        rule_stats = self.risk_rule_engine.get_rule_stats()
        print(f"✓ 风控规则统计: {rule_stats['enabled_rules']}/{rule_stats['total_rules']} 个规则启用")
    
    def test_backtest_integration(self):
        """测试回测集成"""
        print("\n=== 测试回测集成 ===")
        
        # 创建回测配置
        config = BacktestConfig(
            start_date=date(2024, 1, 1),
            end_date=date(2024, 2, 29),
            initial_capital=1000000,
            commission_rate=0.0003
        )
        
        # 创建测试策略
        context = StrategyContext(
            strategy_id='backtest_strategy',
            name='回测策略',
            parameters={'lookback_period': 10, 'threshold': 0.02}
        )
        
        strategy = TestStrategy(context)
        
        # 运行回测
        result = self.backtest_engine.run_backtest(strategy, config)
        
        assert result is not None, "回测结果不应为空"
        assert not result.portfolio_values.empty, "组合价值序列不应为空"
        assert result.performance_metrics, "性能指标不应为空"
        
        print(f"✓ 回测完成")
        print(f"  - 总收益率: {result.performance_metrics.get('total_return', 0):.2%}")
        print(f"  - 年化收益率: {result.performance_metrics.get('annual_return', 0):.2%}")
        print(f"  - 最大回撤: {result.performance_metrics.get('max_drawdown', 0):.2%}")
        print(f"  - 夏普比率: {result.performance_metrics.get('sharpe_ratio', 0):.2f}")
        print(f"  - 总交易次数: {result.performance_metrics.get('total_trades', 0)}")
    
    def test_end_to_end_workflow(self):
        """测试端到端工作流"""
        print("\n=== 测试端到端工作流 ===")
        
        # 1. 数据准备
        symbols = ['000001.SZ']
        end_date = date.today()
        start_date = end_date - timedelta(days=10)
        
        # 获取历史数据
        historical_data = {}
        for symbol in symbols:
            df = self.data_engine.get_daily_data(symbol, start_date, end_date)
            if not df.empty:
                historical_data[symbol] = df
        
        assert historical_data, "历史数据不应为空"
        print(f"✓ 数据准备完成: {len(historical_data)} 个股票")
        
        # 2. 策略创建和执行
        context = StrategyContext(
            strategy_id='e2e_strategy',
            name='端到端测试策略',
            parameters={'lookback_period': 5, 'threshold': 0.01}
        )
        
        strategy = TestStrategy(context)
        strategy.initialize()
        
        # 模拟策略运行
        strategy.on_data(historical_data)
        signals = strategy.generate_signals()
        print(f"✓ 策略执行完成: {len(signals)} 个信号")
        
        # 3. 交易执行
        executed_orders = []
        for signal in signals:
            order = self.trade_executor.execute_signal(signal)
            if order:
                executed_orders.append(order)
        
        print(f"✓ 交易执行完成: {len(executed_orders)} 个订单")
        
        # 4. 风险检查
        portfolio_stats = self.position_manager.get_portfolio_stats()
        
        risk_context = {
            'portfolio_value': portfolio_stats['portfolio_value'],
            'cash': portfolio_stats['cash'],
            'positions': {pos.symbol: {'market_value': pos.market_value} 
                         for pos in self.position_manager.list_positions()},
            'daily_trades': len(executed_orders),
            'max_drawdown': 0.05
        }
        
        violations = self.risk_rule_engine.check_all_rules(risk_context)
        print(f"✓ 风险检查完成: {len(violations)} 个违规")
        
        # 5. 性能分析
        if len(executed_orders) > 0:
            exec_stats = self.trade_executor.get_execution_stats()
            print(f"✓ 执行统计: 成功率 {exec_stats.get('success_rate', 0):.1f}%")
        
        print("✓ 端到端工作流测试完成")


def run_integration_tests():
    """运行集成测试"""
    print("🚀 开始系统集成测试...")
    
    test_suite = TestSystemIntegration()
    test_suite.setup()
    
    try:
        # 运行各项集成测试
        test_suite.test_data_flow_integration()
        test_suite.test_strategy_execution_integration()
        test_suite.test_trading_flow_integration()
        test_suite.test_risk_management_integration()
        test_suite.test_backtest_integration()
        test_suite.test_end_to_end_workflow()
        
        print("\n🎉 所有集成测试通过！")
        return True
        
    except Exception as e:
        print(f"\n❌ 集成测试失败: {e}")
        return False


if __name__ == "__main__":
    success = run_integration_tests()
    exit(0 if success else 1)
