import React, { useState, useEffect } from 'react';
import { Row, Col, Card, Statistic, Table, Alert, Spin } from 'antd';
import { ArrowUpOutlined, ArrowDownOutlined } from '@ant-design/icons';
import ReactECharts from 'echarts-for-react';
import { apiService } from '../services/api';

const Dashboard = () => {
  const [loading, setLoading] = useState(true);
  const [portfolioData, setPortfolioData] = useState({});
  const [performanceData, setPerformanceData] = useState([]);
  const [positions, setPositions] = useState([]);
  const [recentTrades, setRecentTrades] = useState([]);

  useEffect(() => {
    loadDashboardData();
  }, []);

  const loadDashboardData = async () => {
    try {
      setLoading(true);
      
      // 模拟数据加载
      setTimeout(() => {
        setPortfolioData({
          totalValue: 1250000,
          totalReturn: 0.25,
          dailyReturn: 0.012,
          cash: 150000,
          positions: 8
        });

        setPerformanceData([
          { date: '2024-01-01', value: 1000000 },
          { date: '2024-01-15', value: 1050000 },
          { date: '2024-02-01', value: 1120000 },
          { date: '2024-02-15', value: 1180000 },
          { date: '2024-03-01', value: 1250000 }
        ]);

        setPositions([
          { symbol: '000001.SZ', name: '平安银行', quantity: 1000, price: 12.5, value: 12500, pnl: 0.05 },
          { symbol: '000002.SZ', name: '万科A', quantity: 2000, price: 18.2, value: 36400, pnl: -0.02 },
          { symbol: '600000.SH', name: '浦发银行', quantity: 1500, price: 8.8, value: 13200, pnl: 0.08 }
        ]);

        setRecentTrades([
          { id: 1, symbol: '000001.SZ', side: '买入', quantity: 500, price: 12.3, time: '09:30:15' },
          { id: 2, symbol: '600036.SH', side: '卖出', quantity: 1000, price: 35.8, time: '10:15:32' }
        ]);

        setLoading(false);
      }, 1000);
    } catch (error) {
      console.error('加载仪表板数据失败:', error);
      setLoading(false);
    }
  };

  // 组合价值图表配置
  const getPerformanceChartOption = () => ({
    title: {
      text: '组合价值走势',
      left: 'center'
    },
    tooltip: {
      trigger: 'axis',
      formatter: (params) => {
        const data = params[0];
        return `${data.name}<br/>组合价值: ¥${(data.value / 10000).toFixed(2)}万`;
      }
    },
    xAxis: {
      type: 'category',
      data: performanceData.map(item => item.date)
    },
    yAxis: {
      type: 'value',
      axisLabel: {
        formatter: (value) => `¥${(value / 10000).toFixed(0)}万`
      }
    },
    series: [{
      data: performanceData.map(item => item.value),
      type: 'line',
      smooth: true,
      itemStyle: {
        color: '#1890ff'
      },
      areaStyle: {
        color: {
          type: 'linear',
          x: 0,
          y: 0,
          x2: 0,
          y2: 1,
          colorStops: [{
            offset: 0, color: 'rgba(24, 144, 255, 0.3)'
          }, {
            offset: 1, color: 'rgba(24, 144, 255, 0.1)'
          }]
        }
      }
    }]
  });

  // 持仓表格列配置
  const positionColumns = [
    {
      title: '股票代码',
      dataIndex: 'symbol',
      key: 'symbol'
    },
    {
      title: '股票名称',
      dataIndex: 'name',
      key: 'name'
    },
    {
      title: '持仓数量',
      dataIndex: 'quantity',
      key: 'quantity',
      render: (value) => value.toLocaleString()
    },
    {
      title: '当前价格',
      dataIndex: 'price',
      key: 'price',
      render: (value) => `¥${value.toFixed(2)}`
    },
    {
      title: '市值',
      dataIndex: 'value',
      key: 'value',
      render: (value) => `¥${value.toLocaleString()}`
    },
    {
      title: '盈亏',
      dataIndex: 'pnl',
      key: 'pnl',
      render: (value) => (
        <span style={{ color: value >= 0 ? '#3f8600' : '#cf1322' }}>
          {value >= 0 ? '+' : ''}{(value * 100).toFixed(2)}%
        </span>
      )
    }
  ];

  // 交易记录表格列配置
  const tradeColumns = [
    {
      title: '股票代码',
      dataIndex: 'symbol',
      key: 'symbol'
    },
    {
      title: '方向',
      dataIndex: 'side',
      key: 'side',
      render: (value) => (
        <span style={{ color: value === '买入' ? '#3f8600' : '#cf1322' }}>
          {value}
        </span>
      )
    },
    {
      title: '数量',
      dataIndex: 'quantity',
      key: 'quantity'
    },
    {
      title: '价格',
      dataIndex: 'price',
      key: 'price',
      render: (value) => `¥${value.toFixed(2)}`
    },
    {
      title: '时间',
      dataIndex: 'time',
      key: 'time'
    }
  ];

  if (loading) {
    return (
      <div style={{ textAlign: 'center', padding: '50px' }}>
        <Spin size="large" />
        <p style={{ marginTop: '20px' }}>加载中...</p>
      </div>
    );
  }

  return (
    <div>
      <h1>交易仪表板</h1>
      
      {/* 关键指标卡片 */}
      <Row gutter={16} style={{ marginBottom: '24px' }}>
        <Col span={6}>
          <Card>
            <Statistic
              title="组合总价值"
              value={portfolioData.totalValue}
              precision={0}
              valueStyle={{ color: '#3f8600' }}
              prefix="¥"
              suffix="元"
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="总收益率"
              value={portfolioData.totalReturn * 100}
              precision={2}
              valueStyle={{ color: portfolioData.totalReturn >= 0 ? '#3f8600' : '#cf1322' }}
              prefix={portfolioData.totalReturn >= 0 ? <ArrowUpOutlined /> : <ArrowDownOutlined />}
              suffix="%"
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="日收益率"
              value={portfolioData.dailyReturn * 100}
              precision={2}
              valueStyle={{ color: portfolioData.dailyReturn >= 0 ? '#3f8600' : '#cf1322' }}
              prefix={portfolioData.dailyReturn >= 0 ? <ArrowUpOutlined /> : <ArrowDownOutlined />}
              suffix="%"
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="现金余额"
              value={portfolioData.cash}
              precision={0}
              valueStyle={{ color: '#1890ff' }}
              prefix="¥"
              suffix="元"
            />
          </Card>
        </Col>
      </Row>

      {/* 图表和表格 */}
      <Row gutter={16}>
        <Col span={16}>
          <Card title="组合价值走势" style={{ marginBottom: '24px' }}>
            <ReactECharts option={getPerformanceChartOption()} style={{ height: '300px' }} />
          </Card>
        </Col>
        <Col span={8}>
          <Card title="最新交易" style={{ marginBottom: '24px' }}>
            <Table
              dataSource={recentTrades}
              columns={tradeColumns}
              pagination={false}
              size="small"
              rowKey="id"
            />
          </Card>
        </Col>
      </Row>

      <Row gutter={16}>
        <Col span={24}>
          <Card title="持仓明细">
            <Table
              dataSource={positions}
              columns={positionColumns}
              pagination={false}
              rowKey="symbol"
            />
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default Dashboard;
