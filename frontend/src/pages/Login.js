import React, { useState } from 'react';
import { Form, Input, But<PERSON>, Card, message, Spin } from 'antd';
import { UserOutlined, LockOutlined } from '@ant-design/icons';
import { apiService } from '../services/api';

const Login = ({ onLogin }) => {
  const [loading, setLoading] = useState(false);

  const onFinish = async (values) => {
    try {
      setLoading(true);
      
      // 模拟登录API调用
      setTimeout(() => {
        if (values.username === 'admin' && values.password === 'admin123') {
          localStorage.setItem('token', 'mock-jwt-token');
          localStorage.setItem('user', JSON.stringify({
            id: 1,
            username: 'admin',
            name: '管理员'
          }));
          message.success('登录成功');
          onLogin();
        } else {
          message.error('用户名或密码错误');
        }
        setLoading(false);
      }, 1000);
      
    } catch (error) {
      message.error('登录失败，请重试');
      setLoading(false);
    }
  };

  return (
    <div style={{
      height: '100vh',
      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
      display: 'flex',
      justifyContent: 'center',
      alignItems: 'center'
    }}>
      <Card
        title="AI量化交易工具"
        style={{ width: 400, boxShadow: '0 4px 12px rgba(0,0,0,0.15)' }}
      >
        <Form
          name="login"
          onFinish={onFinish}
          autoComplete="off"
          size="large"
        >
          <Form.Item
            name="username"
            rules={[{ required: true, message: '请输入用户名!' }]}
          >
            <Input 
              prefix={<UserOutlined />} 
              placeholder="用户名 (admin)" 
            />
          </Form.Item>

          <Form.Item
            name="password"
            rules={[{ required: true, message: '请输入密码!' }]}
          >
            <Input.Password
              prefix={<LockOutlined />}
              placeholder="密码 (admin123)"
            />
          </Form.Item>

          <Form.Item>
            <Button 
              type="primary" 
              htmlType="submit" 
              loading={loading}
              style={{ width: '100%' }}
            >
              {loading ? <Spin size="small" /> : '登录'}
            </Button>
          </Form.Item>
        </Form>
        
        <div style={{ textAlign: 'center', color: '#666', fontSize: '12px' }}>
          <p>演示账号: admin / admin123</p>
        </div>
      </Card>
    </div>
  );
};

export default Login;
