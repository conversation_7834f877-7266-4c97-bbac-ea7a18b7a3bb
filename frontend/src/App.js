import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { Layout, ConfigProvider } from 'antd';
import zhCN from 'antd/locale/zh_CN';
import 'antd/dist/antd.css';
import './App.css';

// 导入页面组件
import Dashboard from './pages/Dashboard';
import DataManagement from './pages/DataManagement';
import StrategyManagement from './pages/StrategyManagement';
import TradingManagement from './pages/TradingManagement';
import RiskManagement from './pages/RiskManagement';
import BacktestAnalysis from './pages/BacktestAnalysis';
import Login from './pages/Login';

// 导入布局组件
import AppHeader from './components/Layout/AppHeader';
import AppSidebar from './components/Layout/AppSidebar';

const { Content } = Layout;

function App() {
  const [collapsed, setCollapsed] = React.useState(false);
  const [isAuthenticated, setIsAuthenticated] = React.useState(false);

  // 检查用户认证状态
  React.useEffect(() => {
    const token = localStorage.getItem('token');
    if (token) {
      setIsAuthenticated(true);
    }
  }, []);

  // 如果未认证，显示登录页面
  if (!isAuthenticated) {
    return (
      <ConfigProvider locale={zhCN}>
        <Login onLogin={() => setIsAuthenticated(true)} />
      </ConfigProvider>
    );
  }

  return (
    <ConfigProvider locale={zhCN}>
      <Router>
        <Layout style={{ minHeight: '100vh' }}>
          <AppSidebar collapsed={collapsed} />
          <Layout className="site-layout">
            <AppHeader 
              collapsed={collapsed} 
              onCollapse={setCollapsed}
              onLogout={() => setIsAuthenticated(false)}
            />
            <Content style={{ margin: '24px 16px', padding: 24, background: '#fff' }}>
              <Routes>
                <Route path="/" element={<Dashboard />} />
                <Route path="/dashboard" element={<Dashboard />} />
                <Route path="/data" element={<DataManagement />} />
                <Route path="/strategy" element={<StrategyManagement />} />
                <Route path="/trading" element={<TradingManagement />} />
                <Route path="/risk" element={<RiskManagement />} />
                <Route path="/backtest" element={<BacktestAnalysis />} />
              </Routes>
            </Content>
          </Layout>
        </Layout>
      </Router>
    </ConfigProvider>
  );
}

export default App;
