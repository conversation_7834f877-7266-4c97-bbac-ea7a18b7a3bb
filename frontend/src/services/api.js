import axios from 'axios';

// 创建axios实例
const api = axios.create({
  baseURL: process.env.REACT_APP_API_BASE_URL || 'http://localhost:8000',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// 请求拦截器
api.interceptors.request.use(
  (config) => {
    // 添加认证token
    const token = localStorage.getItem('token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// 响应拦截器
api.interceptors.response.use(
  (response) => {
    return response.data;
  },
  (error) => {
    if (error.response?.status === 401) {
      // 未授权，清除token并跳转到登录页
      localStorage.removeItem('token');
      window.location.href = '/login';
    }
    return Promise.reject(error);
  }
);

// API服务类
class ApiService {
  // 用户认证相关
  async login(credentials) {
    return api.post('/api/v1/auth/login', credentials);
  }

  async logout() {
    return api.post('/api/v1/auth/logout');
  }

  async getUserInfo() {
    return api.get('/api/v1/auth/me');
  }

  // 数据管理相关
  async getStockList(params = {}) {
    return api.get('/api/v1/data/stocks', { params });
  }

  async getRealtimeQuotes(symbols) {
    return api.get('/api/v1/data/quote', { 
      params: { symbols: symbols.join(',') } 
    });
  }

  async getKlineData(symbol, startDate, endDate, period = 'daily') {
    return api.get('/api/v1/data/kline', {
      params: { symbol, start_date: startDate, end_date: endDate, period }
    });
  }

  async queryData(request) {
    return api.post('/api/v1/data/query', request);
  }

  // 策略管理相关
  async getStrategies() {
    return api.get('/api/v1/strategies');
  }

  async getStrategy(strategyId) {
    return api.get(`/api/v1/strategies/${strategyId}`);
  }

  async createStrategy(strategy) {
    return api.post('/api/v1/strategies', strategy);
  }

  async updateStrategy(strategyId, strategy) {
    return api.put(`/api/v1/strategies/${strategyId}`, strategy);
  }

  async deleteStrategy(strategyId) {
    return api.delete(`/api/v1/strategies/${strategyId}`);
  }

  async startStrategy(strategyId) {
    return api.post(`/api/v1/strategies/${strategyId}/start`);
  }

  async stopStrategy(strategyId) {
    return api.post(`/api/v1/strategies/${strategyId}/stop`);
  }

  // 交易管理相关
  async getOrders(params = {}) {
    return api.get('/api/v1/orders', { params });
  }

  async createOrder(order) {
    return api.post('/api/v1/orders', order);
  }

  async cancelOrder(orderId) {
    return api.delete(`/api/v1/orders/${orderId}`);
  }

  async getPositions() {
    return api.get('/api/v1/positions');
  }

  async getTrades(params = {}) {
    return api.get('/api/v1/trades', { params });
  }

  // 风险管理相关
  async getRiskMetrics() {
    return api.get('/api/v1/risk/metrics');
  }

  async getRiskRules() {
    return api.get('/api/v1/risk/rules');
  }

  async updateRiskRule(ruleId, rule) {
    return api.put(`/api/v1/risk/rules/${ruleId}`, rule);
  }

  async getRiskViolations(hours = 24) {
    return api.get('/api/v1/risk/violations', { 
      params: { hours } 
    });
  }

  // 回测分析相关
  async runBacktest(config) {
    return api.post('/api/v1/backtest/run', config);
  }

  async getBacktestResults(backtestId) {
    return api.get(`/api/v1/backtest/results/${backtestId}`);
  }

  async optimizeParameters(config) {
    return api.post('/api/v1/backtest/optimize', config);
  }

  // 系统监控相关
  async getSystemStatus() {
    return api.get('/api/v1/system/status');
  }

  async getSystemMetrics() {
    return api.get('/api/v1/system/metrics');
  }

  // WebSocket连接管理
  createWebSocket(endpoint, onMessage, onError) {
    const wsUrl = `ws://localhost:8002/ws/${endpoint}`;
    const ws = new WebSocket(wsUrl);

    ws.onopen = () => {
      console.log(`WebSocket连接已建立: ${endpoint}`);
    };

    ws.onmessage = (event) => {
      try {
        const data = JSON.parse(event.data);
        onMessage(data);
      } catch (error) {
        console.error('WebSocket消息解析失败:', error);
      }
    };

    ws.onerror = (error) => {
      console.error('WebSocket错误:', error);
      if (onError) onError(error);
    };

    ws.onclose = () => {
      console.log(`WebSocket连接已关闭: ${endpoint}`);
    };

    return ws;
  }

  // 订阅实时数据
  subscribeRealtimeData(symbols, onData) {
    const ws = this.createWebSocket('data', onData);
    
    ws.onopen = () => {
      // 发送订阅消息
      ws.send(JSON.stringify({
        type: 'subscribe',
        symbols: symbols,
        data_type: 'quote',
        interval: 60
      }));
    };

    return ws;
  }
}

// 导出API服务实例
export const apiService = new ApiService();
export default api;
